// HRV测试调试工具 - 简化版本
// 用于调用Android WebView JavascriptInterface

console.log("🚀 HRV调试工具启动...");

// 检查Android接口
function checkAndroidInterface() {
    console.log("🔍 检查Android接口...");
    
    // 检查常见接口名称
    const names = ['Android', 'AndroidInterface', 'NativeInterface', 'WebViewInterface', 'JSBridge'];
    
    for (const name of names) {
        if (typeof window[name] !== 'undefined') {
            console.log(`✅ 找到接口: ${name}`);
            console.log(`📱 可用方法:`, Object.getOwnPropertyNames(window[name]));
            return name;
        }
    }
    
    // 如果没找到，扫描所有对象
    console.log("🔍 扫描所有window对象...");
    const allProps = Object.getOwnPropertyNames(window);
    
    for (const name of allProps) {
        try {
            const obj = window[name];
            if (obj && typeof obj === 'object' && obj !== null) {
                // 检查是否有我们需要的方法
                if (typeof obj.saveAssessmentResult === 'function' || 
                    typeof obj.stopGazeTracking === 'function') {
                    console.log(`🎯 找到目标接口: ${name}`);
                    console.log(`📱 方法列表:`, Object.getOwnPropertyNames(obj));
                    return name;
                }
            }
        } catch (e) {
            // 忽略访问错误
        }
    }
    
    console.warn("⚠️ 未找到Android接口");
    console.log("💡 请确保:");
    console.log("   1. 在Android WebView中运行");
    console.log("   2. 已调用addJavascriptInterface");
    console.log("   3. 页面已完全加载");
    
    return null;
}

// 安全调用Android方法
function safeCallAndroid(interfaceName, methodName, ...args) {
    try {
        if (!interfaceName) {
            console.error(`❌ 接口名称为空`);
            return false;
        }
        
        const androidInterface = window[interfaceName];
        if (!androidInterface) {
            console.error(`❌ 接口 ${interfaceName} 不存在`);
            return false;
        }
        
        const method = androidInterface[methodName];
        if (typeof method !== 'function') {
            console.error(`❌ 方法 ${methodName} 不存在或不是函数`);
            return false;
        }
        
        console.log(`🔄 调用 ${interfaceName}.${methodName}...`);
        method.apply(androidInterface, args);
        console.log(`✅ 调用成功`);
        return true;
        
    } catch (e) {
        console.error(`❌ 调用失败: ${e.message}`);
        return false;
    }
}

// 调用saveAssessmentResult
function callSaveAssessmentResult(resultJson) {
    console.log("💾 准备保存评估结果...");
    console.log(`📊 数据长度: ${resultJson.length}`);
    console.log(`📊 数据预览: ${resultJson.substring(0, 100)}...`);
    
    const interfaceName = checkAndroidInterface();
    if (interfaceName) {
        return safeCallAndroid(interfaceName, 'saveAssessmentResult', resultJson);
    }
    return false;
}

// 调用stopGazeTracking
function callStopGazeTracking() {
    console.log("🛑 准备停止眼动追踪...");
    
    const interfaceName = checkAndroidInterface();
    if (interfaceName) {
        return safeCallAndroid(interfaceName, 'stopGazeTracking');
    }
    return false;
}

// 测试数据
function createTestData() {
    return {
        userId: "test_user_001",
        testType: "HRV_ASSESSMENT",
        startTime: Date.now() - 300000,
        endTime: Date.now(),
        results: {
            heartRate: 72,
            hrvScore: 85,
            stressLevel: "low",
            recommendations: ["保持当前状态", "适量运动"]
        },
        metadata: {
            deviceType: "web",
            browserInfo: navigator.userAgent,
            timestamp: Date.now()
        }
    };
}

// 测试保存功能
function testSave() {
    console.log("🧪 测试保存功能...");
    const testData = createTestData();
    return callSaveAssessmentResult(JSON.stringify(testData));
}

// 测试停止追踪
function testStop() {
    console.log("🧪 测试停止追踪...");
    return callStopGazeTracking();
}

// 显示帮助
function showHelp() {
    console.log(`
🔧 HRV调试工具使用说明:

基本功能:
• checkAndroidInterface() - 检查Android接口
• callSaveAssessmentResult(jsonString) - 保存评估结果
• callStopGazeTracking() - 停止眼动追踪

测试功能:
• testSave() - 测试保存功能
• testStop() - 测试停止功能
• createTestData() - 创建测试数据

示例:
testSave();
testStop();
callSaveAssessmentResult('{"test": "data"}');
    `);
}

// 初始化
console.log("✅ HRV调试工具加载完成");
checkAndroidInterface();
showHelp();

// 导出到全局
window.checkAndroidInterface = checkAndroidInterface;
window.callSaveAssessmentResult = callSaveAssessmentResult;
window.callStopGazeTracking = callStopGazeTracking;
window.testSave = testSave;
window.testStop = testStop;
window.showHelp = showHelp;
