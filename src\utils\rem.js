// src/utils/rem.js（新建这个文件）
function setRemBase() {
  const baseWidth = 1920 // 设计稿宽度，例如你是按 iPhone 6 做的
  const baseFontSize = 16 // 理想根字体大小（在 baseWidth 下）

  const html = document.documentElement
  const clientWidth = html.clientWidth

  const scale = clientWidth / baseWidth
  html.style.fontSize = baseFontSize * scale + 'px'
}

// 初始化 + resize 监听
function initRem() {
  setRemBase()
  window.addEventListener('resize', setRemBase)
}

export default initRem
