// jest.config.cjs
module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.vue$': '@vue/vue3-jest', // 转译 .vue 文件
    '^.+\\.js$': 'babel-jest',      // 转译 .js 文件
  },
  moduleFileExtensions: ['js', 'vue'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1', // 设置 @ 符号的路径别名
  },
  collectCoverage: true,
  collectCoverageFrom: [
    'src/utils/**/*.{js,vue}',
    '!src/utils/**/index.js',
  ],
  transformIgnorePatterns: ['/node_modules/'], // 确保转译您的代码
};
