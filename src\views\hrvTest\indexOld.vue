<template>
	<div class="container">
		<div class="header">
			<div class="title-container">
				<!-- <h1 v-if="hrvWords['hrv_title_key']">{{ hrvWords['hrv_title_key'] }}</h1> -->
				<h1>{{ $t("hrv_title_key") }}</h1>
			</div>
		</div>

		<!-- Floating window with prompts based on face status -->
		<div
			v-if="
				!isLoading &&
				faceStatus !== FACE_APPROPRIATE &&
				(stage === RecordStage.DETECT ||
					stage === RecordStage.IMAGE_DISPLAY)
			"
			class="floating-prompt"
		>
			<div v-if="faceStatus === FACE_NOT_DETECTED">
				{{ $t("face_not_detected_key") }}
				{{ $t("face_not_detected_key2") }}
			</div>
			<div v-if="faceStatus === FACE_TOO_SMALL">
				{{ $t("face_too_small_key") }}
			</div>
			<div v-if="faceStatus === FACE_TOO_CLOSE_TO_EDGE">
				{{ $t("face_too_close_to_edge_key") }}
			</div>
			<div v-if="faceStatus === FACE_TOO_LARGE">
				{{ $t("face_too_large_key") }}
			</div>
		</div>

		<div class="hrv_audio">
			<audio ref="hrvtestAudioRef" :src="hrvAudioMp3" autoplay></audio>
		</div>

		<div class="hrv_audio">
			<audio
				ref="hrvtestImageTipAudioRef"
				:src="hrvImageTipAudioMp3"
			></audio>
		</div>

		<div class="main-container">
			<!-- 图片显示 -->
			<img
				v-if="stage === RecordStage.IMAGE_DISPLAY && currentImage"
				:src="`/image_new/${currentImage}`"
				class="image-display"
			/>

			<div class="video-wrapper-container">
				<div v-if="stage === RecordStage.DETECT" class="introduction">
					<div>{{ $t("hrv_stage1_key") }}</div>
					<!-- <div>{{ $t("hrv_stage2_key") }}</div> -->
				</div>
				<VideoWrapper
					@faceStatus="handleFaceStatus"
					class="pip-video"
					ref="faceView"
					:stage="stage"
				/>

				<!-- 倒计时和图片信息显示 -->
				<div
					v-if="stage === RecordStage.IMAGE_DISPLAY"
					class="info-display"
				>
					<div>
						{{ $t("hrv_stage2_timeleft_key") }} {{ totalTimeLeft }}
						{{ $t("seconds_key") }}
					</div>
					<!-- <div>图片倒计时：{{ imageTimeLeft }} 秒</div> -->
					<!-- <div>当前图片：{{ currentImage }}</div> -->
					<div>{{ $t("hrv_stage2_leftimage_key") }}</div>
					<!-- <div>{{ $t("hrv_stage2_fps_key") }} {{ fps }}</div> -->
				</div>

				<div
					v-if="stage === RecordStage.IMAGE_DISPLAY"
					class="chart-display"
				>
					<HrvChatFace
						:timeDiffArray="timeDiffArray"
						:rgbMeanArray="rgbMeanArray"
					/>
				</div>
				<!-- <div
					v-if="!isLoading && stage === RecordStage.DETECT"
					class="start-test-button-container"
				>
					<button @click="startTest" class="start-test-button">
						{{ $t("start_key") }}
					</button>
				</div> -->
				<!-- 添加进度条 -->
				<div v-if="isLoading" class="progress-bar-container">
					<p>{{ $t("model_initing_key") }}</p>
					<div class="progress-bar">
						<div class="loading-progress"></div>
					</div>
				</div>

				<!-- 报告生成提示及进度条 -->
				<div
					v-if="stage === RecordStage.REPORT_GENERATING"
					class="progress-bar-container"
				>
					<p>{{ $t("report_generating_key") }}</p>
					<div class="progress-bar">
						<div class="generating-progress"></div>
					</div>
				</div>

				<div
					v-if="stage === RecordStage.REPORT_ERROR"
					class="generate-report-error"
				>
					<p>{{ $t("report_generate_exception_key") }}</p>
					<button @click="handleExit" class="exit-button">
						{{ $t("exit_key") }}
					</button>
				</div>

				<div
					v-if="stage === RecordStage.REPORT_COMPLETE_LATER"
					class="generate-report-complete-later"
				>
					<p>{{ $t("report_generate_complete_report_later_key") }}</p>
					<button @click="handleExit" class="exit-button">
						{{ $t("return_key") }}
					</button>
				</div>
			</div>
		</div>
		<FinshModal
			:visible="centerDialogVisible"
      :mode="hrvDetectMode"
			@goBack="handleGoBack"
			@onFinish="handleExit"
		/>
	</div>
</template>

<script setup>
import { onMounted, onBeforeMount } from "vue"
import VideoWrapper from "@/components/hrv/VideoWrapper.vue"
import HrvChatFace from "@/components/hrv/HrvChatFace.vue"
import FinshModal from "@/components/hrv/FinishModal.vue"
import { useHrvTest } from "@/views/hrvTest/hooks.js"
import {
	FACE_NOT_DETECTED,
	FACE_TOO_SMALL,
	FACE_TOO_LARGE,
	FACE_APPROPRIATE,
	FACE_TOO_CLOSE_TO_EDGE,
} from "../../constants/faceStatusConstants.js"
import { RecordStage } from "../../constants/recordStage.js"

const {
	centerDialogVisible,
	faceStatus,
	isLoading,
	stage,
	currentImage,
	totalTimeLeft,
	faceView,
	timeDiffArray,
	rgbMeanArray,
	fps,
	hrvtestAudioRef,
  hrvDetectMode,
	hrvAudioMp3,
	hrvtestImageTipAudioRef,
	hrvImageTipAudioMp3,
	handleFaceStatus,
	startTest,
	handleExit,
	handleGoBack,
	initAudio,
	initHrv,
} = useHrvTest()



onMounted(() => {
	initHrv()
})
</script>

<style scoped>
.simulate-button {
	opacity: 0;
}
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	width: 100vw;
	box-sizing: border-box;
	background-color: #f3f4f5;
}

.header {
	display: flex;
	justify-content: center;
	align-items: center;
	color: #19191a;
	padding: 15px 0;
	border-radius: 10px;
}

.title-container {
	display: flex;
	justify-content: center;
	align-items: center;
}

h1 {
	margin: 0;
}

.version {
	font-size: 0.8em;
	color: #999;
	margin-left: 5px; /* 给版本信息和标题之间增加一些空间 */
}

.main-container {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 85%;
	width: 100%;
}

.image-display {
	width: 80%;
	height: 100%;
	object-fit: contain;
	left: 0;
	top: 0;
}

.video-wrapper-container {
	height: 100%;
	position: relative;
	padding: 0px;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
}

.introduction {
	color: #19191a;
	padding: 0 0 20px 0;
	font-size: 1.5rem; /* Large font */
	text-align: center;
}

/* 小窗口样式 */
.pip-video {
	width: 80%; /* 默认宽度占据100% */
	height: auto; /* 高度根据内容自动调整 */
	max-height: 40%; /* 高度不能超过父容器的100% */
	max-width: none; /* 允许宽度自由调整 */
	border: 2px solid #108c65;
	border-radius: 15px;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
	object-fit: contain; /* 确保内容按比例缩放 */
}

.info-display {
	margin-top: 20px;
	width: 100%;
	text-align: left;
	background-color: rgba(23, 215, 226, 0.7); /* 半透明背景 */
	color: #19191a;
	padding: 10px;
	border-radius: 10px;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
}

.info-display div {
	margin-bottom: 3px; /* 每行之间的间距 */
}

.chart-display {
	margin-top: 20px;
	margin-bottom: 5px;
}

.floating-prompt {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent background */
	color: white;
	padding: 20px;
	border-radius: 10px;
	font-size: 1.5rem; /* Large font */
	text-align: center;
	z-index: 20; /* Ensure it's above other elements */
	width: 80%; /* Adjust width as needed */
	max-width: 600px; /* Max width to control the prompt size */
}

.progress-bar-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%; /* Fill the main container */
	text-align: center;
	font-size: 1.5rem;
	color: #19191a;
}

.progress-bar {
	width: 80%;
	height: 20px;
	background-color: #e0e0e0;
	border-radius: 10px;
	overflow: hidden;
	margin-top: 20px;
}

.loading-progress {
	height: 100%; /* 进度条的高度 */
	background-color: #4caf50; /* 绿色进度条 */
	animation: loading 10s linear forwards; /* 10秒内线性从0%到100% */
}

.generating-progress {
	height: 100%; /* 进度条的高度 */
	background-color: #4caf50; /* 蓝色进度条 */
	animation: generating 5s linear forwards; /* 5秒内线性从0%到100% */
}

@keyframes loading {
	0% {
		width: 0%; /* 初始宽度为 0% */
	}
	100% {
		width: 100%; /* 10 秒后宽度为 100% */
	}
}

@keyframes generating {
	0% {
		width: 0%; /* 初始宽度为 0% */
	}
	100% {
		width: 100%; /* 5 秒后宽度为 100% */
	}
}

.start-test-button-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
}

.start-test-button {
	padding: 10px 20px;
	font-size: 1.5rem;
	background-color: #17d7ea;
	color: white;
	border: none;
	border-radius: 10px;
	cursor: pointer;
	transition: background-color 0.3s ease;
	margin: 20px 0;
}

.start-test-button:hover {
	background-color: #14bccf;
}

.generate-report-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	margin-top: 20px;
	gap: 20px;
	height: 100%; /* Fill the main container */
	text-align: center;
	font-size: 1.5rem;
	color: #19191a;
}

.generate-report-error p {
	color: #ff4c4c; /* 红色用于警告文案 */
}

.generate-report-complete-later {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	margin-top: 20px;
	gap: 20px;
	height: 100%; /* Fill the main container */
	text-align: center;
	font-size: 1.5rem;
	color: #19191a;
}

.exit-button {
	padding: 10px 20px;
	font-size: 1.5rem;
	background-color: #17d7ea;
	color: white;
	border: none;
	border-radius: 10px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.exit-button:hover {
	background-color: #14bccf;
}

.input-profile-info {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.input-profile-items {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	width: 100%;
	max-width: 800px; /* 根据需要调整最大宽度 */
	font-size: 1.5rem;
}

.input-profile-item {
	display: flex;
	flex-direction: column;
	margin: 10px; /* 根据需要调整间距 */
	width: 100%;
}

.input-profile-item lable {
	margin-bottom: 5px; /* 根据需要调整标签与输入框的间距 */
}

.hrv_audio {
	display: none;
}
</style>
