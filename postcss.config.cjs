// module.exports = {
//   plugins: {
//     'postcss-px-to-viewport': {
//       unitToConvert: 'px', // 需要转换的单位，默认为"px"
//       viewportWidth: 1280, // 视窗的宽度，对应的是我们设计稿的宽度，一般是750
//       unitPrecision: 3, // 指定`px`转换为视窗单位值的小数位数（很多时候无法整除）
//       propList: ['*'], // 能转化为vw的属性列表
//       viewportUnit: 'vw', // 指定需要转换成的视窗单位，建议使用vw
//       fontViewportUnit: 'vw', // 字体使用的视口单位
//       selectorBlackList: [], // 指定不转换为视窗单位的类，可以自定义，可以无限添加,建议定义一至两个通用的类名
//       minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
//       mediaQuery: true, // 允许在媒体查询中转换`px`
//       replace: true, //  是否直接更换属性值，而不添加备用属性
//       exclude: undefined, // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
//       landscape: false,
//     },
//   },
// };
