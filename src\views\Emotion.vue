<template>
  <div class="emotion-capture">
    <h1 class="fixed-header">{{$t('emotion_title_key')}}</h1>

    <div class="content">
      <div v-if="!isCapturing && !isGeneratingReport && !isHiddenReportFlag" class="introduction">
        <!-- <h3>{{$t('emotion_sys_desc_key') }}</h3> -->
        <!-- <p>{{ $t('emotion_sys_welcome_key') }}</p> -->
        <!-- <ul>
          <li>{{ $t('emotion_sys_desc1_key') }}</li>
          <li>{{ $t('emotion_sys_desc2_key') }}</li>
          <li>{{ $t('emotion_sys_desc3_key') }}
            <ul>
              <li>{{ $t('emotion_sys_desc3_li1_key') }}</li>
              <li>{{ $t('emotion_sys_desc3_li2_key') }}</li>
              <li>{{ $t('emotion_sys_desc3_li3_key') }}</li>
              <li>{{ $t('emotion_sys_desc3_li4_key') }}</li>
            </ul>
          </li>
        </ul>
        <p>{{ $t('emotion_sys_pre_start_key') }}</p> -->
        <p>{{ $t('emotion_sys_welcome_key1') }}</p>
      </div>
    </div>

    <div :class="{ 'row-layout': isCapturing }">
      <div class="emotion-capture_wrapper" :style="wrapperStyle">
        <div class="video-container" :style="videoContainerStyle">
          <video ref="videoRef" @loadedmetadata="onStreamLoaded" :class="{ hidden: !isCapturing }"></video>
          <canvas ref="canvasRef" class="hidden"></canvas>
        </div>

        <div v-if="isCapturing" class="metrics">
          <!-- <p>Resolution: {{ resolution }}</p> -->
          <!-- <p>Frame Rate: {{ frameRate.toFixed(2) }} FPS</p> -->
          <p>Frame Captured: {{ frameTotal }} FPS</p>
          <!-- <p>Frame Analyzed: {{ frameAnalyzed }} FPS</p> -->
        </div>
      </div>

      <div v-if="emotionResult && isCapturing" class="result">
        <div v-if="lastCapturedImage" class="captured-image">
          <img :src="lastCapturedImage" alt="Captured emotion"/>
        </div>
        <h3>Detected Emotion</h3>
        <!-- <div class="emotion-display">
          <span class="emotion">{{ emotionResult.expression }}</span>
          <div class="confidence-bar">
            <div class="confidence-fill" :style="{ width: `${emotionResult.confidence * 100}%` }"></div>
          </div>
          <span class="confidence">{{ (emotionResult.confidence * 100).toFixed(2) }}%</span>
        </div> -->
      </div>
    </div>

    <div v-if="error" class="error">{{ error }}</div>

    <div v-if="isGeneratingReport" class="report-generation">
      <p>{{ $t('report_generating_key') }}</p>
      <div class="progress-bar-container">
        <div class="progress-bar">
          <div class="progress" :style="{ width: `${progressPercentage}%` }"></div>
        </div>
      </div>
    </div>

    <div v-if="isHiddenReportFlag" class="generate-report-complete-later">
      <p>{{ $t('report_generate_complete_report_later_key')}}</p>
      <button @click="handleExit" class="exit-button">{{ $t('return_key')}}</button>
    </div>

    <div v-if="!isHiddenReportFlag" class="controls">
      <button @click="toggleCapture" :class="{ 'btn-stop': isCapturing }" :disabled="isGeneratingReport">
        {{ isCapturing ? $t('finish_key') : $t('start_key') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import { v4 as uuidv4 } from 'uuid';
import { useEmotion } from '@/composables/useEmotion';
import { useEmotionStore } from '@/stores/emotionStore';

const router = useRouter();
const emotionStore = useEmotionStore();

const { emotionResult, enSessionId, isHiddenReport, error, capturedImageData, sendFrameToBackend } = useEmotion();

const videoRef = ref(null);
const canvasRef = ref(null);
const stream = ref(null);
const intervalId = ref(null);
const isCapturing = ref(false);
const resolution = ref('N/A');
const frameRate = ref(0);
const frameCount = ref(0);
const frameTotal = ref(0);
const frameAnalyzed = ref(0);
const lastFrameTime = ref(0);
const videoWidth = ref(0);
const videoHeight = ref(0);
const lastCapturedImage = ref(null);
const userId = ref('unknown');
const sessionId = ref(null);

const maxFrameNum = ref(3);
const analysisResults = ref([]);

const lastCaptureTime = ref(0);
const frameInterval_ms = 5000;

const isGeneratingReport = ref(false);
const reportProgress = ref(0);
const progressPercentage = computed(() => reportProgress.value * 100);

const reportStartTime = ref(0);
const reportDuration = 5000; // 5 seconds in milliseconds
const reportTimeoutId = ref(null);
const captureFlag = ref(true)

const deviceId = ref("")
const isHiddenReportFlag = ref(0)

const videoContainerStyle = computed(() => {
  if (videoWidth.value && videoHeight.value) {
    return {
      width: `${videoWidth.value}px`,
      height: `${videoHeight.value}px`,
      // width: `512px`,
      // height: `384px`,
      maxWidth: '100%',
      maxHeight: '70vh',
    };
  }
  return {};
});

const wrapperStyle = computed(() => ({
  backgroundColor: isCapturing.value ? '#e8f5e9' : 'transparent',
}));

const toggleCapture = async () => {
  if (isCapturing.value) {
    stopCapture();
  } else {
    await startCapture();
  }
};

const handleExit = () => {
  if (typeof android !== 'undefined' && android && typeof android.onFinish === 'function') {
    android.onFinish();
  } else {
    const params = new URLSearchParams(window.location.search)
    const urlParam = params.toString()
    router.replace('/emotion?' + urlParam).then(() => {
      window.location.reload();  // 强制页面刷新，重置所有组件
    });
  }
};

const startCapture = async () => {
  try {
    sessionId.value = uuidv4();
    stream.value = await navigator.mediaDevices.getUserMedia({video: true});

    // navigator.mediaDevices.getUserMedia({
    //   video: {
    //     facingMode: "user"
    //   },
    //   audio: true
    // })
    // .then(stream => {
    //   videoRef.value.srcObject = stream;

    //   onStreamLoaded();
    //   videoRef.value.addEventListener('loadeddata', function() {
    //     console.log("start camera")
    //     videoRef.value.play();
    //     lastCaptureTime.value = performance.now();
    //     isCapturing.value = true;
    //     error.value = null;
    //     captureFrames();
    //     startFrameRateCalculation();
    //   });
    // })
    // .catch(error => {
    //   console.error('摄像头访问失败：', error);
    // });

    videoRef.value.srcObject = stream.value;
    await videoRef.value.play();

    // console.log("bababaa")
    // const uploader = new StreamUploader({
    //   uploadUrl: '/api/v1/upload/video/stream',
    //   timeslice: 1000
    // });
    // console.log("lala")
    // await uploader.startRecording(stream.value);
    // console.log("start recording")

    onStreamLoaded();
    lastCaptureTime.value = performance.now();
    isCapturing.value = true;
    error.value = null;
    captureFrames();
    startFrameRateCalculation();
  } catch (err) {
    console.error('Error accessing the camera:', err);
    error.value = 'Failed to access the camera. Please check your permissions.';
  }
};

const stopCapture = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop());
    stream.value = null;
  }
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  isCapturing.value = false;
  emotionResult.value = null;
  lastCapturedImage.value = null;
  resolution.value = 'N/A';
  frameRate.value = 0;
  videoWidth.value = 0;
  videoHeight.value = 0;

  if (analysisResults.value.length > 0) {
    isGeneratingReport.value = true;
    reportStartTime.value = performance.now();
    reportProgress.value = 0;

    preloadImages();

    reportTimeoutId.value = setTimeout(() => {
      isGeneratingReport.value = false;
      emotionStore.setAnalysisResults(analysisResults.value);

      if (isHiddenReport.value) {
        isHiddenReportFlag.value = isHiddenReport.value
        return;
      }

      router.push({name: 'EmotionReport', query: {"en_session_id": enSessionId.value}});
    }, reportDuration);

    requestAnimationFrame(animateProgress);
  }
};

const preloadImages = () => {
  analysisResults.value.forEach(result => {
    const origImg = new Image();
    const meshImg = new Image();
    origImg.src = result.orig_blob_url;
    meshImg.src = result.mesh_blob_url;

    origImg.onload = () => emotionStore.setImageLoaded(result.id, 'orig');
    meshImg.onload = () => emotionStore.setImageLoaded(result.id, 'mesh');
  });
};

const animateProgress = (timestamp) => {
  if (!isGeneratingReport.value) return;

  const elapsed = timestamp - reportStartTime.value;
  reportProgress.value = Math.min(elapsed / reportDuration, 1);

  if (reportProgress.value < 1) {
    requestAnimationFrame(animateProgress);
  }
};

const onStreamLoaded = () => {
  // videoWidth.value = videoRef.value.videoWidth;
  // videoHeight.value = videoRef.value.videoHeight;
  videoWidth.value = 512
  videoHeight.value = 384
  canvasRef.value.width = videoWidth.value;
  canvasRef.value.height = videoHeight.value;
  resolution.value = `${videoWidth.value}x${videoHeight.value}`;
};

const captureFrames = async () => {
  if (!isCapturing.value) return;

  // const video_value = videoRef.value;
  // console.log(video_value.width);
  const now = performance.now();
  if ((now - lastCaptureTime.value) >= frameInterval_ms && captureFlag.value == true) {
    captureFlag.value = false;
    captureAndProcessFrame().then(() => {
      lastCaptureTime.value = now;
      captureFlag.value = true;
    })
  }
  requestAnimationFrame(captureFrames);
  frameCount.value++;
  frameTotal.value++;
};

const captureAndProcessFrame = async () => {
  if (!isCapturing.value) return;
  const context = canvasRef.value.getContext('2d');
  context.drawImage(videoRef.value, 0, 0, canvasRef.value.width, canvasRef.value.height);
  const fullImageData = canvasRef.value.toDataURL('image/png');
  const imageData = fullImageData.split(',')[1];
  await sendFrameToBackend(Date.now(), userId.value, sessionId.value, imageData, deviceId.value);

  console.log("emotionResult.value")
  console.log(emotionResult.value)
  if (emotionResult.value) {
    analysisResults.value.push(emotionResult.value);
    frameAnalyzed.value++;

    if (maxFrameNum.value > 0 && analysisResults.value.length >= maxFrameNum.value) {
      stopCapture();
      // setTimeout(() => {
      //   stopCapture();
      // }, 1000);
    }
  }
};

const startFrameRateCalculation = () => {
  setInterval(() => {
    const now = performance.now();
    const elapsed = now - lastFrameTime.value;
    frameRate.value = frameCount.value / (elapsed / 1000);
    frameCount.value = 0;
    lastFrameTime.value = now;
  }, 1000); // Update frame rate every second
};

const resetAllData = () => {
  isCapturing.value = false;
  resolution.value = 'N/A';
  frameRate.value = 0;
  frameCount.value = 0;
  frameTotal.value = 0;
  frameAnalyzed.value = 0;
  lastFrameTime.value = 0;
  videoWidth.value = 0;
  videoHeight.value = 0;
  lastCapturedImage.value = null;
  sessionId.value = null;
  analysisResults.value = [];
  lastCaptureTime.value = 0;
  isGeneratingReport.value = false;
  reportProgress.value = 0;
  reportStartTime.value = 0;

  emotionStore.resetState();

  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop());
    stream.value = null;
  }
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  if (reportTimeoutId.value) {
    clearTimeout(reportTimeoutId.value);
    reportTimeoutId.value = null;
  }

  error.value = null;
};

watch(capturedImageData, (newImageData) => {
  if (newImageData) {
    lastCapturedImage.value = `data:image/png;base64,${newImageData}`;
  }
});

onMounted(() => {
  resetAllData();

  const urlParams = new URLSearchParams(window.location.search);
  const device_id = urlParams.get('device_id');
  if (device_id) {
    deviceId.value = device_id
  }

  const user_token = urlParams.get('token');
  if (user_token) {
    window.localStorage.setItem("user_token", user_token)
  }

});

onActivated(() => {
  resetAllData();
});

onUnmounted(() => {
  stopCapture();
});

</script>

<style scoped>
body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

.emotion-capture {
  width: 100vw;
  height: 100vh;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px 0;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  margin-top: 60px; /* Adjust this value based on the height of your header */
  padding: 20px;
}

.introduction {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.introduction h3 {
  color: #333;
  margin-bottom: 15px;
}

.introduction ul {
  padding-left: 20px;
}

.introduction ul ul {
  margin-top: 10px;
}

.introduction p:last-of-type {
  margin-top: 10px; /* Increase this value for more spacing */
}

.row-layout {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.emotion-capture_wrapper {
  flex: 1;
  padding: 15px;
  border-radius: 8px;
  height: 100%;
}

.result {
  flex: 1;
  margin-left: 20px;
  padding: 15px;
  border-radius: 8px;
  height: 100%;
  background-color: #e8f5e9;
}

.video-container {
  position: relative;
  background-color: #f0f0f0;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 20px;
  flex: 1;
  aspect-ratio: 16 / 9;
  max-height: 70vh;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scaleX(-1);
}

.controls {
  margin-top: 20px;
  text-align: center;
}

button {
  min-width: 120px;
  padding: 12px 24px;
  font-size: 16px;
  background-color: #17d7ea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button:hover {
  background-color: #14bccf;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

button.btn-stop {
  background-color: #f44336;
}

button.btn-stop:hover {
  background-color: #d32f2f;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  box-shadow: none;
}

@media (max-width: 480px) {
  button, .btn-stop {
    min-width: 100px;
    padding: 10px 20px;
  }
}

.emotion-display {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.emotion {
  font-weight: bold;
  font-size: 18px;
  margin-right: 10px;
}

.confidence-bar {
  flex-grow: 1;
  height: 20px;
  background-color: #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.5s ease-in-out;
}

.confidence {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
}

.captured-image {
  /* margin-top: 15px; */
  text-align: center;
  height: 384px;
}

.captured-image img {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error {
  margin-top: 20px;
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 5px;
  text-align: center;
  width: 100%;
  max-width: 500px;
}

.hidden {
  display: none;
}

.metrics {
  margin-top: 10px;
  /* padding: 10px; */
  height: 1px;
  background-color: rgb(232, 245, 233);
  border-radius: 5px;
  font-size: 14px;
  color: rgb(232, 245, 233);
  width: 100%;
}

.metrics p {
  margin: 5px 0;
}

.report-generation {
  width: 100%;
  margin-top: 20px;
  text-align: center;
}

.report-generation p {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
}

.progress-bar-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.progress-bar {
  width: 60%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.5s linear;
}

.generate-report-complete-later {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content:  flex-start;
  margin-top: 20px;
  gap: 20px;
  height: 100%; /* Fill the main container */
  text-align: center;
  font-size: 1.5rem;
  color: #19191a;
}
</style>