// hrv-module.js

// 如果需要 FFT 和 Sample Entropy 等高级数学计算，需要引入相应的库
// 这里我们使用 mathjs 和 fft-js 库
import { create, all } from 'mathjs';
import { fft } from 'fft-js';

// 创建一个 MathJS 实例
const config = {};
const math = create(all, config);

// 主函数：计算 HRV 指标
export function calculateHRVMetrics(rrIntervals) {
  if (!Array.isArray(rrIntervals) || rrIntervals.length < 2) {
    throw new Error('RR 间期数据必须是包含至少两个值的数组。');
  }

  // 将 RR 间期从毫秒转换为秒
  const rrInSeconds = rrIntervals.map(rr => rr / 1000);

  // 时域指标
  const timeDomain = calculateTimeDomainMetrics(rrInSeconds);

  // 频域指标
  const frequencyDomain = calculateFrequencyDomainMetrics(rrInSeconds);

  // 非线性指标
  const nonlinear = calculateNonlinearMetrics(rrInSeconds);

  // 返回所有指标
  return {
    timeDomain,
    frequencyDomain,
    nonlinear
  };
}

// 时域指标计算
function calculateTimeDomainMetrics(rrIntervals) {
  const N = rrIntervals.length;
  const meanRR = math.mean(rrIntervals);
  const sdnn = math.std(rrIntervals, 'uncorrected');
  const differences = rrIntervals.slice(1).map((rr, i) => rr - rrIntervals[i]);
  const squaredDiffs = differences.map(diff => diff * diff);
  const rmssd = Math.sqrt(math.mean(squaredDiffs));

  // NN50 和 pNN50
  const nn50Count = differences.filter(diff => Math.abs(diff) > 0.05).length;
  const pnn50 = (nn50Count / (N - 1)) * 100;

  // 平均心率（每分钟心跳数）
  const averageHeartRate = 60 / meanRR;

  return {
    SDNN: sdnn,
    RMSSD: rmssd,
    NN50: nn50Count,
    pNN50: pnn50,
    averageHeartRate
  };
}

// 频域指标计算
function calculateFrequencyDomainMetrics(rrIntervals) {
  // 需要将 RR 间期转换为等间隔的心率时间序列
  const interpolatedRR = interpolateRR(rrIntervals);

  // 确保输入数据长度为 2 的幂次方
  const dataLength = interpolatedRR.length;
  const nearestPowerOfTwo = Math.pow(2, Math.floor(Math.log2(dataLength)));
  const fftInput = interpolatedRR.slice(0, nearestPowerOfTwo);

  // 检查数据格式
  fftInput.forEach((value, index) => {
    if (typeof value !== 'number' || isNaN(value)) {
      throw new Error(`Invalid data at index ${index}: ${value}`);
    }
  });

  // 应用 Hamming 窗口
  const windowedRR = fftInput.map((value, index) => {
    return (
        value *
        (0.54 -
            0.46 * Math.cos((2 * Math.PI * index) / (fftInput.length - 1)))
    );
  });

  // 执行 FFT
  const phasors = fft(windowedRR);

  // 计算频率和幅度
  const frequencies = phasors.slice(0, phasors.length / 2).map((complex, index) => {
    const frequency = (index * fs) / fftInput.length;
    const amplitude = Math.sqrt(complex[0] ** 2 + complex[1] ** 2);
    return { frequency, amplitude };
  });

  // 定义频率范围（Hz）
  const VLF_RANGE = [0.0033, 0.04];
  const LF_RANGE = [0.04, 0.15];
  const HF_RANGE = [0.15, 0.4];

  // 计算功率谱密度
  const psd = frequencies.map(f => ({
    frequency: f.frequency,
    power: (f.amplitude ** 2) / interpolatedRR.length
  }));

  // 计算各频段的功率
  const totalPower = psd.reduce((sum, p) => sum + p.power, 0);
  const vlfPower = psd.filter(p => p.frequency >= VLF_RANGE[0] && p.frequency < VLF_RANGE[1])
  .reduce((sum, p) => sum + p.power, 0);
  const lfPower = psd.filter(p => p.frequency >= LF_RANGE[0] && p.frequency < LF_RANGE[1])
  .reduce((sum, p) => sum + p.power, 0);
  const hfPower = psd.filter(p => p.frequency >= HF_RANGE[0] && p.frequency < HF_RANGE[1])
  .reduce((sum, p) => sum + p.power, 0);

  const lfHfRatio = lfPower / hfPower;

  return {
    totalPower,
    VLF: vlfPower,
    LF: lfPower,
    HF: hfPower,
    LF_HF_Ratio: lfHfRatio
  };
}

// 自定义线性插值函数
function linearInterpolation(x, y, xNew) {
  const yNew = [];
  let i = 0;
  const n = x.length;

  for (let xi of xNew) {
    while (i < n - 2 && xi > x[i + 1]) {
      i++;
    }
    const x0 = x[i];
    const x1 = x[i + 1];
    const y0 = y[i];
    const y1 = y[i + 1];
    const slope = (y1 - y0) / (x1 - x0);
    const yi = y0 + slope * (xi - x0);
    yNew.push(yi);
  }
  return yNew;
}

// RR 间期插值（用于频域分析）
function interpolateRR(rrIntervals) {
  // 这里使用简单的线性插值，生成等间隔的 RR 序列
  // 实际应用中，可以使用更复杂的插值方法
  const totalTime = rrIntervals.reduce((sum, rr) => sum + rr, 0);
  const fs = 4; // 采样频率，通常为 4Hz 或更高
  const timeAxis = [];
  let currentTime = 0;
  for (let rr of rrIntervals) {
    timeAxis.push(currentTime);
    currentTime += rr;
  }

  const interpolatedTimeAxis = math.range(0, totalTime, 1 / fs, true).toArray();
  const interpolatedRR = linearInterpolation(timeAxis, rrIntervals, interpolatedTimeAxis);

  return interpolatedRR;
}

// 非线性指标计算
function calculateNonlinearMetrics(rrIntervals) {
  const differences = rrIntervals.slice(1).map((rr, i) => rr - rrIntervals[i]);

  // Poincaré 散点图参数 SD1 和 SD2
  const sd1 = Math.sqrt(0.5) * math.std(differences, 'uncorrected');
  const sd2 = Math.sqrt(2 * math.std(rrIntervals, 'uncorrected') ** 2 - 0.5 * sd1 ** 2);

  // 样本熵
  const sampleEntropy = calculateSampleEntropy(rrIntervals, 2, 0.2 * math.std(rrIntervals, 'uncorrected'));

  return {
    SD1: sd1,
    SD2: sd2,
    SampleEntropy: sampleEntropy
  };
}

// 计算样本熵的函数
function calculateSampleEntropy(data, m, r) {
  const n = data.length;
  let B = 0.0;
  let A = 0.0;

  const dataVectors = [];

  for (let i = 0; i < n - m + 1; i++) {
    dataVectors.push(data.slice(i, i + m));
  }

  for (let i = 0; i < dataVectors.length; i++) {
    const template = dataVectors[i];
    let count = 0;
    for (let j = 0; j < dataVectors.length; j++) {
      if (i === j) continue;
      const distance = Math.max(...template.map((v, idx) => Math.abs(v - dataVectors[j][idx])));
      if (distance < r) {
        count++;
      }
    }
    B += count;
  }

  // m + 1 阶
  const dataVectors2 = [];
  for (let i = 0; i < n - m; i++) {
    dataVectors2.push(data.slice(i, i + m + 1));
  }

  for (let i = 0; i < dataVectors2.length; i++) {
    const template = dataVectors2[i];
    let count = 0;
    for (let j = 0; j < dataVectors2.length; j++) {
      if (i === j) continue;
      const distance = Math.max(...template.map((v, idx) => Math.abs(v - dataVectors2[j][idx])));
      if (distance < r) {
        count++;
      }
    }
    A += count;
  }

  if (B === 0 || A === 0) {
    return undefined;
  } else {
    return -Math.log(A / B);
  }
}

export const saveRGBMeansToCSV = () => {
  const csvContent = rgb_means.map(mean => mean.join(",")).join("\n");
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'rgb_means.csv';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
