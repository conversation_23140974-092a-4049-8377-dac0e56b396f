<template>
	<div class="report-container" id="report">
		<h1>{{ t("hrv_report_title_key") }}</h1>
		<BasicInfo :info="basicInfo" />
		<template v-if="showHrvGraphic">
			<HrvMetricsTable :hrvInfo="hrvInfo" />
			<HrvChartTimeDomain :RRIntervals="RRIntervals" />
			<HrvChartFreqDomain :hrv_data="hrv_data" class="page-break" />
		</template>
		<HrvChartPressure :PressureInfo="pressureInfo" />
		<HrvChartNerves :NerveInfo="nerveInfo" />
		<HrvMetricsInterp :PressureInfo="pressureInfo" :NerveInfo="nerveInfo" />

		<div class="signature" v-if="showHrvBottomSignature">
			<div class="signature_content">
				<p>
					{{ t("report_doctor_key") }}{{ t("colon_key") }}
					<!-- [{{
						t("signature_key")
					}}] -->
				</p>
				<p>
					{{ t("report_date_key") }}{{ t("colon_key") }}
					<!-- [{{
						t("date_key")
					}}] -->
				</p>
				<p>
					{{ t("report_hospital_seal") }}{{ t("colon_key") }}
					<!-- [{{
						t("seal_key")
					}}] -->
				</p>
			</div>
		</div>

		<div class="button-group">
			<button @click="handleExit" class="button">
				{{ t("exit_key") }}
			</button>
			<button @click="exportToPDF" class="button">
				{{ t("print_report_key") }}
			</button>
			<!-- <button @click="handleGoBack" class="button">
				{{ t("return_key") }}
			</button> -->
		</div>
	</div>
</template>

<script setup>
import { onMounted } from "vue"
import { useI18n } from "vue-i18n"

// import {
// 	Chart as ChartJS,
// 	Title,
// 	Tooltip,
// 	Legend,
// 	LineElement,
// 	CategoryScale,
// 	LinearScale,
// 	PointElement,
// } from "chart.js"

import BasicInfo from "./components/BasicInfo.vue"
import HrvMetricsTable from "./components/HrvMetricsTable.vue"
import HrvChartTimeDomain from "./components/HrvChartTimeDomain.vue"
import HrvChartFreqDomain from "./components/HrvChartFreqDomain.vue"
import HrvChartPressure from "./components/HrvChartPressure.vue"
import HrvChartNerves from "./components/HrvChartNerves.vue"
import HrvMetricsInterp from "./components/HrvMetricsInterp.vue"
import { useHrvReport } from "@/views/hrvReport/hooks"
// import {
//   Chart as ChartJS,
//   Title,
//   Tooltip,
//   Legend,
//   LineElement,
//   CategoryScale,
//   LinearScale,
//   PointElement,
// } from 'chart.js';

const { t } = useI18n()

const {
	showHrvBottomSignature,
	showHrvGraphic,
	hrv_data,
	basicInfo,
	RRIntervals,
	hrvInfo,
	nerveInfo,
	pressureInfo,
	exportToPDF,
	handleExit,
	handleGoBack,
	initHrvData,
} = useHrvReport()
// Register Chart.js components using the global Chart object
if (window.Chart) {
	window.Chart.register(
		window.Chart.Title,
		window.Chart.Tooltip,
		window.Chart.Legend,
		window.Chart.LineElement,
		window.Chart.CategoryScale,
		window.Chart.LinearScale,
		window.Chart.PointElement
	)
}

onMounted(async () => {
	await initHrvData()
})
</script>

<style scoped>
/* 样式保持不变 */
.report-container {
	max-width: 900px;
	margin: 0 auto;
	padding: 20px;
	width: 100%;
	font-family: Arial, sans-serif;
	background-color: white;
}

h1 {
	text-align: center;
	margin-bottom: 20px;
}

table {
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20px;
}

table th,
table td {
	border: 1px solid #ddd;
	padding: 8px;
	text-align: left;
}

.button-group {
	display: flex;
	justify-content: center;
	gap: 10px;
	margin-top: 20px;
}

.button {
	padding: 10px 20px;
	background-color: #007bff;
	color: white;
	border: none;
	border-radius: 5px;
	font-size: 16px;
	cursor: pointer;
	min-width: 150px;
	text-align: center;
}

.button:hover {
	background-color: #0056b3;
}

.page-break {
	page-break-before: always;
}

.signature {
	/* padding: 1rem; */
}

.signature_content {
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	padding: 1rem;
	margin-bottom: 1rem;
}

@media print {
	.export-button {
		display: none;
	}
	.button-group {
		display: none;
	}
}
</style>
