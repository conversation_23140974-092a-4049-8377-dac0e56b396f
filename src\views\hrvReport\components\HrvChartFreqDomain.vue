<template>
	<div>
		<table class="rr-chart-table">
			<thead>
				<tr>
					<th colspan="6" class="chart-header">
						{{ $t("frequency_domain_analysis_key") }}
					</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td colspan="6">
						<div class="chart-container">
							<canvas id="psdChart"></canvas>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue"
import { Chart, LineElement, CategoryScale, LinearScale, Title, Tooltip, Legend, Filler } from 'chart.js';

// 注册 Chart.js 组件
Chart.register(LineElement, CategoryScale, LinearScale, Title, Tooltip, Legend, Filler);



const props = defineProps({
	// RRIntervals: Array,
	hrv_data: Object,
})

const psdChart = ref(null)
const welch_psd_info = ref(null)

const FREQUENCY_RANGES = {
	ULF: [0.0001, 0.003],
	VLF: [0.003, 0.04],
	LF: [0.04, 0.15],
	HF: [0.15, 0.4],
	VHF: [0.4, 0.5],
}

const calculatePSD = (hrv_data) => {
	// const interpolatedRR = interpolateRR(rrIntervals);
	// const fftData = fft(interpolatedRR);

	welch_psd_info.value = hrv_data.welchPsd

	let frequencies = []
	let psdValues = []

	if (!welch_psd_info.value) {
		return { frequencies, psdValues, psdValues }
	}

	frequencies = welch_psd_info.value[0]
	psdValues = welch_psd_info.value[1]

	const psdByBand = {
		ULF: [],
		VLF: [],
		LF: [],
		HF: [],
		VHF: [],
	}

	frequencies.forEach((freq, index) => {
		const psdValue = psdValues[index]
		// if (psdValue <= 0.0001) return;

		if (freq >= FREQUENCY_RANGES.ULF[0] && freq < FREQUENCY_RANGES.ULF[1]) {
			psdByBand.ULF.push(psdValue)
			psdByBand.VLF.push(0)
			psdByBand.LF.push(0)
			psdByBand.HF.push(0)
			psdByBand.VHF.push(0)
		} else if (
			freq >= FREQUENCY_RANGES.VLF[0] &&
			freq < FREQUENCY_RANGES.VLF[1]
		) {
			psdByBand.VLF.push(psdValue)
			psdByBand.LF.push(0)
			psdByBand.HF.push(0)
			psdByBand.VHF.push(0)
		} else if (
			freq >= FREQUENCY_RANGES.LF[0] &&
			freq < FREQUENCY_RANGES.LF[1]
		) {
			psdByBand.LF.push(psdValue)
			psdByBand.HF.push(0)
			psdByBand.VHF.push(0)
		} else if (
			freq >= FREQUENCY_RANGES.HF[0] &&
			freq < FREQUENCY_RANGES.HF[1]
		) {
			psdByBand.HF.push(psdValue)
			psdByBand.VHF.push(0)
		} else if (
			freq >= FREQUENCY_RANGES.VHF[0] &&
			freq <= FREQUENCY_RANGES.VHF[1]
		) {
			psdByBand.VHF.push(psdValue)
		}
	})

	return { frequencies, psdByBand, psdValues }
}

const interpolateRR = (rrIntervals) => {
	const interpolatedRR = new Array(rrIntervals.length)
		.fill(0)
		.map(() => Math.random())
	return interpolatedRR
}

const fft = (data) => {
	return data.map(() => Math.random())
}

const createPSDChart = () => {
	const ctx = document.getElementById("psdChart").getContext("2d")

	if (!props.hrv_data) {
		return
	}

	const { frequencies, psdByBand, psdValues } = calculatePSD(props.hrv_data)

	const filteredFrequency = frequencies.filter((frenq, index) => frenq <= 0.4)

	const changeColor = (ctx, value) => {
		console.log(filteredFrequency[ctx.p0.parsed.x])
		const freq = filteredFrequency[ctx.p0.parsed.x]

		if (freq < FREQUENCY_RANGES.ULF[1]) {
			return "rgba(128, 0, 128, 1)"
		} else if (
			freq >= FREQUENCY_RANGES.VLF[0] &&
			freq < FREQUENCY_RANGES.VLF[1]
		) {
			return "rgba(0, 128, 255, 1)"
		} else if (
			freq >= FREQUENCY_RANGES.LF[0] &&
			freq < FREQUENCY_RANGES.LF[1]
		) {
			return "rgba(0, 255, 0, 1)"
		} else if (
			freq >= FREQUENCY_RANGES.HF[0] &&
			freq < FREQUENCY_RANGES.HF[1]
		) {
			return "rgba(255, 128, 0, 1)"
		} else if (
			freq >= FREQUENCY_RANGES.VHF[0] &&
			freq <= FREQUENCY_RANGES.VHF[1]
		) {
			return "rgba(255, 0, 0, 1)"
		}
		return "rgba(255, 0, 0, 1)"
	}

	psdChart.value = new Chart(ctx, {
		type: "line",
		data: {
			labels: filteredFrequency,
			datasets: [
				// {
				//   label: 'ULF',
				//   data: psdByBand.ULF,
				//   backgroundColor: 'rgba(128, 0, 128, 0.6)',
				//   // borderColor: 'rgba(128, 0, 128, 1)',
				//   fill: true,
				//   pointRadius: 0,
				// },
				{
					label: "VLF",
					data: psdByBand.VLF,
					backgroundColor: "rgba(0, 128, 255, 0.6)",
					// borderColor: 'rgba(0, 128, 255, 1)',
					fill: true,
					pointRadius: 0,
				},
				{
					label: "LF",
					data: psdByBand.LF,
					backgroundColor: "rgba(0, 255, 0, 0.6)",
					// borderColor: 'rgba(0, 255, 0, 1)',
					fill: true,
					pointRadius: 0,
				},
				{
					label: "HF",
					data: psdByBand.HF,
					backgroundColor: "rgba(255, 128, 0, 0.6)", //'rgba(255, 128, 0, 0.6)'
					// borderColor: 'rgba(255, 128, 0, 1)',
					// segment: {
					//   borderColor: ctx => changeColor(ctx, 'rgb(0,0,0,0.2)'),
					// },
					// spanGaps: true,
					fill: true,
					pointRadius: 0,
				},
				// {
				//   label: 'VHF',
				//   data: psdByBand.VHF,
				//   backgroundColor: 'rgba(255, 0, 0, 0.6)',
				//   // borderColor: 'rgba(255, 0, 0, 1)',
				//   fill: true,
				//   pointRadius: 0,
				// },
			],
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			plugins: {
				legend: {
					display: true,
					position: "right",
				},
			},
			scales: {
				x: {
					title: {
						display: true,
						text: "Frequency (Hz)",
					},
					ticks: {
						// stepSize: 0.05,
						// autoSkip: true,
						maxTicksLimit: 10,
						min: 0,
						max: 0.4,
						callback: function (value, index, ticks) {
							return filteredFrequency[value].toFixed(2)
						},
					},
				},
				y: {
					title: {
						display: true,
						text: "Spectrum (ms²/Hz)",
					},
					beginAtZero: true,
				},
			},
		},
	})
}

onMounted(() => {
	createPSDChart()
})

watch(
	() => props.hrv_data,
	() => {
		// if (psdChart.value) {
		//   psdChart.value.destroy();
		//   createPSDChart();
		// }
		createPSDChart()
	}
)
</script>

<style scoped>
.rr-chart-table {
	width: 100%;
	border-collapse: collapse;
}

.rr-chart-table th,
.rr-chart-table td {
	border: 1px solid #ddd;
	padding: 8px;
	text-align: center;
}

.chart-container {
	width: 100%;
	height: 400px; /* 确保图表容器有固定高度 */
}

canvas {
	display: block;
	width: 100% !important;
	height: 100% !important;
}

.chart-header {
	text-align: left !important;
	background-color: #17d7e2;
	color: white;
	font-weight: bold;
}

@media print {
	.chart-container {
		height: 290px;
	}
}
</style>
