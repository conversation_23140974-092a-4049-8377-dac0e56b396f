<template>
	<el-dialog
		:close-on-click-modal="false"
		v-model="dialogVisible"
		width="400"
		center
		:show-close="false"
	>
		<template #header>
			<div class="custom-header" v-if="showClose">
				<img
					src="@/assets/hrv/testFinishClose.png"
					alt="close-icon"
					class="close-icon"
					@click="emit('onCancel')"
				/>
			</div>
		</template>
		<div class="dialog-body">
			<span class="title-tip">{{ $t("tips_key") }}</span>
			<span class="finish-tip">{{ content }}</span>
			<div class="btn-box">
				<div @click="emit('onCancel')" class="finish-btn">
					{{ $t("hrv_back_btn") }}
				</div>
				<div @click="emit('onFinish')" class="finish-btn">
					{{ $t("hrv_finish_btn") }}
				</div>
        <div class="baseline"></div>

			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, defineEmits, defineProps, computed, watch } from "vue"

const emit = defineEmits(["onFinish", "onCancel"]) // 定义 emit 方法
const props = defineProps({
	visible: Boolean,
	showClose: Boolean,
	content: String,
})
const dialogVisible = ref(props.visible)

watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue
})

</script>

<style scoped lang="scss">
.custom-header {
	display: flex;
	justify-content: flex-end;
	.close-icon {
		width: 20px;
		height: 20px;
		cursor: pointer;
	}
}
.dialog-body {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	font-weight: 500;
	color: #333333;
  font-size: 2.25rem;
	.title-tip {
		font-weight: 500;
		font-size: 3rem;
		color: #eb4e89;
    margin-bottom: 1.88rem;
    
	}
	.btn-box {
    margin-top: 5.5rem;
		display: flex;
		justify-content: space-around;
		align-content: center;
		width: 100%;
		border-top: 0.06rem solid #e3e9ef;
    padding: 1.88rem;
    padding-bottom: 0;
    position: relative;
    .baseline{
      position: absolute;
      left: 50%;
      top: 3.4rem;
      transform: translate(-50%, -50%);
      width: 0.06rem;
      height: 5.31rem;
      background: #e3e9ef
    }
		.finish-btn {
			flex: 1;
			display: flex;
			justify-content: center;
			align-content: center;
			font-weight: 500;
			font-size: 2.25rem;
			color: #666666;
			letter-spacing: 2px;
		}
		.finish-btn:nth-child(2) {
      color: #EB4E89;
    }
	}
}
</style>
