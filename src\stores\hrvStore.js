import { defineStore } from "pinia"
import { ElMessage } from "element-plus"
import { api } from "@/api"
import { RecordStage } from "@/constants/recordStage"
export const useHrvStore = defineStore("hrv", {
	state: () => ({
		loadModelFlag: false,
		timeDiffArray: [],
		rgbMeanArray: [],
		faceToken: "",
	}),
	getters: {
		LoadModelFlag: (state) => state.loadModelFlag,
		getTimeDiffArray: (state) => state.timeDiffArray,
		getRgbMeanArray: (state) => state.rgbMeanArray,
		getFaceToken: (state) => state.faceToken,
	},

	actions: {
		setLoadModelFlag(flag) {
			this.loadModelFlag = flag
		},
		setTimeDiffArray(timeDiffArray) {
			this.timeDiffArray = timeDiffArray
		},
		setRgbMeanArray(rgbMeanArray) {
			this.rgbMeanArray = rgbMeanArray
		},
		setFaceToken(faceToken) {
			this.faceToken = faceToken
		},
		async sendDataToBackend(router, route, gazePoints) {
			let backendStatus
			const stopRecordingWithError = async (errorMessage, type) => {
				switch (type) {
					case "network":
						backendStatus = RecordStage.NETWORK_ERROR
						break
					case "submit":
						backendStatus = RecordStage.SUBMITTING_DATA_ERROR
						break
					case "expire":
						backendStatus = RecordStage.USER_EXPIRE
						break
					case "frontend":
						backendStatus = RecordStage.FRONTEND_ERROR
						break
				}
				router.push({
					name: "HrvFinished",
					query: {
						...route.query,
						process: backendStatus,
					},
				})
				ElMessage.error(errorMessage)
			}

			try {
				const resultArray = this.timeDiffArray.map((time, index) => [
					time,
					...this.rgbMeanArray[index],
				])
				const requestInfo = {
					signal_result: resultArray,
					faceEntityId: this.faceToken,
					gazePoints,
				}

				const response = await api.post("/api/hrv/rppg", requestInfo)
				console.log("🚀 ~ sendDataToBackend ~ response:", response)
				if (response.status !== 200) {
					return await stopRecordingWithError(
						response.status,
						"network"
					)
				}

				const apiResult = response.data
				if (Number(apiResult.code) === 401) {
					return await stopRecordingWithError(
						apiResult?.msg,
						"expire"
					)
				}
				if (Number(apiResult.code) !== 0) {
					return await stopRecordingWithError(
						apiResult?.msg,
						"submit"
					)
				}

				const {
					reportId: reportIdFromApi,
					showLocalHrvReport,
					hrvDetectMode,
				} = apiResult.data
				backendStatus = RecordStage.REPORT_COMPLETE

				if (showLocalHrvReport) {
					router.push({
						name: "HrvReport",
						query: { reportId: reportIdFromApi },
					})
				} else {
					router.push({
						name: "HrvFinished",
						query: {
							...route.query,
							detectMode: hrvDetectMode,
							process: backendStatus,
						},
					})
				}
			} catch (error) {
				console.error("Failed to send data: ", error)
				let type = "frontend"
				// 接口超时
				if (error?.code === "ECONNABORTED") {
					type = "network"
				}
				await stopRecordingWithError(error, type)
			}
		},
	},
})
