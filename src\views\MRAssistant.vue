<template>
  <div class="container">
    <div class="header">
      <h1>智能病历助理</h1>
      <span class="version">（{{ version }}{{ asr_type }}）</span>
    </div>
    <div class="main-container">
      <div class="profile-info">
        <div>
          <label for="username">门诊号：</label>
          <el-input type="text" v-model="clinicNo" class="profile-input"></el-input>
        </div>
      </div>
      <div class="input-section">
          <div class="input-container">
            <p>对话记录：</p>
            <el-input type="textarea" v-model="transcription" class="custom-input"
                      :disabled="isGeneratingSummary"></el-input>
          </div>
          <div class="input-container">
            <p>诊断摘要：</p>
            <!-- <div v-html="renderedSummary" class="summary-container"
                 :disabled="isGeneratingSummary || recording"></div> -->
            <el-input type="textarea" class="custom-input" v-model="renderedSummary"
                 :disabled="isGeneratingSummary || recording"></el-input>
          </div>
        </div>
      <div class="button-container">
        <el-button class="custom-button" type="primary" @click="handleRecording" :disabled="isGeneratingSummary">
          {{ recording ? '生成摘要' : '开始记录' }}
        </el-button>
        <el-button class="custom-button" type="primary" @click="handleExit" >退出</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, watch, onMounted, onBeforeUnmount, nextTick} from 'vue';
import { useRouter } from 'vue-router';
import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import axios from 'axios';
import {marked} from 'marked';
import { ElMessageBox } from 'element-plus';
import { AudioStreamUploader } from '@/utils/record_audio.js';

let speechConfig = null
let speechRecognizer = null;
const router = useRouter();
const version = ref('v0.1');

const recording = ref(false);
const isGeneratingSummary = ref(false);
const transcription = ref('');
const tmpTranscription = ref('');
const summary = ref('');
const renderedSummary = ref('');
const clinicNo = ref('');
const copilotType = ref('');
const audioUploader = ref();

const params = new URLSearchParams(window.location.search);
const channelNo = ref(params.get('channel_no'));
const deviceId = ref(params.get('device_id'));
const lang = ref(params.get('lang'));
const asr_type = ref(params.get('asr_type'));

// // 讯飞语音识别参数
// let recorder;
// let iatWS;
// let countdownInterval;

const handleRecording = () => {
  if (!recording.value) {
    startRecording();
  } else {
    stopRecognition();
  }
};

const updateSpeechToken = () => {
  setInterval(async () => {
    if (speechRecognizer) {
      const speechData = await get_speech_token();
      const token = speechData.token;
      const region = speechData.region;
      if (token) {
        speechRecognizer.authorizationToken = token
      }
    }
  }, 300000);
}

// const connectWebSocketXF = async () => {
//     const url = await get_xf_rtasr_url()
//     const websocketUrl = url;
//     if ("WebSocket" in window) {
//       iatWS = new WebSocket(websocketUrl);
//     } else if ("MozWebSocket" in window) {
//       iatWS = new MozWebSocket(websocketUrl);
//     } else {
//       return;
//     }
//     console.log("CONNECTING")
//     iatWS.onopen = (e) => {
//       // 开始录音
//       recorder.start({
//         sampleRate: 16000,
//         frameSize: 1280,
//       });
//     };
//     iatWS.onmessage = (e) => {
//       renderResultXF(e.data);
//     };
//     iatWS.onerror = (e) => {
//       console.error(e);
//       recorder.stop();
//       console.log("CLOSED")
//     };
//     iatWS.onclose = (e) => {
//       recorder.stop();
//       console.log("CLOSED")
//     };
// }

// const renderResultXF = (resultData) => {
//     let jsonData = JSON.parse(resultData);
//     if (jsonData.action == "started") {
//       // 握手成功
//       console.log("握手成功");
//     } else if (jsonData.action == "result") {
//       const data = JSON.parse(jsonData.data)
//       console.log(data)
//       // 转写结果
//       let resultTextTemp = ""
//       data.cn.st.rt.forEach((j) => {
//         j.ws.forEach((k) => {
//           k.cw.forEach((l) => {
//             resultTextTemp += l.w;
//           });
//         });
//       });
//       if (data.cn.st.type == 0) {
//         // 【最终】识别结果：
//         resultTextTemp = resultTextTemp.replace(/[。！？.!?]/g, '$&\n');
//         if (asr_type.value == "xf_rtasr") {
//           transcription.value += resultTextTemp;
//         } else {
//           tmpTranscription.value += resultTextTemp + "\n";
//           console.log("xf_rtasr: " + tmpTranscription.value)
//         }
//       }
//       resultTextTemp = ""


//     } else if (jsonData.action == "error") {
//       // 连接发生错误
//       console.log("出错了:", jsonData);
//     }
// }

const startRecording = async () => {
  recording.value = true;

  const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  audioUploader.value = new AudioStreamUploader({
    uploadUrl: 'https://grace.ybbywb.com:8082/api/v1/upload/audio/record',
    // uploadUrl: 'http://127.0.0.1:18082/api/v1/upload/audio/record',
    timeslice: 10000
  });
  audioUploader.value.stream = stream;

  audioUploader.value.startRecording().then(() => {
    console.log("start recording")
  })

  const speechData = await get_speech_token();
  const token = speechData.token;
  const region = speechData.region;
  speechConfig = SpeechSDK.SpeechConfig.fromAuthorizationToken(token, region);
  speechConfig.speechRecognitionLanguage = 'zh-CN';
  speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_SingleLanguageIdPriority, 'SpeakerDiarization');
  speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_SpeakerDiarizationEnabled, 'true');
  const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
  speechRecognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

  transcription.value = '';
  tmpTranscription.value = '';
  summary.value = '';
  speechRecognizer.recognized = (s, e) => {
    if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
      if (!asr_type.value) {
        transcription.value += e.result.text + '\n';  // 确保最终结果按行添加并换行
      } else {
        tmpTranscription.value += e.result.text + '\n';
        console.log("ms: " + tmpTranscription.value);
      }
    } else if (e.result.reason == SpeechSDK.ResultReason.NoMatch) {
        console.log("NOMATCH: Speech could not be recognized.");
    }
  };

  speechRecognizer.sessionStopped = (s, e) => {
    console.log("语音识别停止")
    // ElMessageBox.alert("语音识别停止", '提示', {
    //     confirmButtonText: '确定',
    // });
  }
  speechRecognizer.canceled = (s, e) => {
    console.log("语音识别取消")
    const error_info = getAzureErrorDetails(e)
    console.log(error_info)
    // ElMessageBox.alert("语音识别取消 - " + error_info, '提示', {
    //     confirmButtonText: '确定',
    // });
  }

  speechRecognizer.startContinuousRecognitionAsync();


  // 科大讯飞语音识别接入

  // recorder = new RecorderManager("/rtasr");
  // recorder.onStart = () => {
  //   console.log("OPEN");
  // }

  // recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
  //   if (iatWS.readyState === iatWS.OPEN) {
  //     iatWS.send(new Int8Array(frameBuffer));
  //     if (isLastFrame) {
  //       iatWS.send('{"end": true}');
  //       console.log("CLOSING");
  //     }
  //   }
  // };
  // recorder.onStop = () => {
  //   clearInterval(countdownInterval);
  // };

  // connectWebSocketXF()
};

const getAzureErrorDetails = (result) => {
  if (result.reason === SpeechSDK.ResultReason.Canceled) {
    const cancellation = SpeechSDK.CancellationDetails.fromResult(result)
    if (cancellation.reason === SpeechSDK.CancellationReason.Error) {
      return `CANCELED: ErrorCode=${enumValueToName(SpeechSDK.CancellationErrorCode, cancellation.ErrorCode)} - ${cancellation.errorDetails}`
    } else {
      return `CANCELED: Reason=${enumValueToName(SpeechSDK.CancellationReason, cancellation.reason)} - ${result.errorDetails}`
    }
  }
  return result.errorDetails
}
const enumValueToName = (obj, value) => {
  return Object.keys(obj)[Object.values(obj).findIndex(x => x === value)]
}

const get_speech_token = async () => {
  try {
    const authorizationEndpoint = "https://grace.ybbywb.com:8082/api/v1/get-speech-token";
    // const authorizationEndpoint = "http://127.0.0.1:18082/api/v1/get-speech-token";
    const res = await axios.post(authorizationEndpoint);
    const token = res.data.token;
    const region = res.data.region;
    return { token, region };
  } catch (err) {
    console.log(err);
  }
}

// const get_xf_rtasr_url = async () => {
//   try {
//     const authorizationEndpoint = "https://grace.ybbywb.com:8082/api/v1/get_xf_rtasr_url";
//     // const authorizationEndpoint = "http://127.0.0.1:18082/api/v1/get_xf_rtasr_url";
//     const res = await axios.post(authorizationEndpoint);
//     const url = res.data.url;
//     return url;
//   } catch (err) {
//     console.log(err);
//   }
// }

const stopRecognition = async () => {

  if (clinicNo.value == "") {
    ElMessageBox.alert("门诊号不能为空", '提示', {
        confirmButtonText: '确定',
    });
    return;
  }

  recording.value = false;
  if (speechRecognizer) {
    speechRecognizer.stopContinuousRecognitionAsync(() => {
      speechRecognizer.close();
      speechRecognizer = null;
    });
  }

  // // 讯飞语音识别结束处理
  // if (recorder) {
  //   recorder.stop();
  // }

  isGeneratingSummary.value = true;
  await generateSummary();
  isGeneratingSummary.value = false;
};

const generateSummary = async () => {
  try {

    const headers = {
      'DEVICE-ID': deviceId.value
    }
    if (lang.value != null) {
      headers["Accept-Language"] = lang.value
    }

    const response = await axios.post('https://grace.ybbywb.com:8082/chat/all_messages', {
    // const response = await axios.post('http://127.0.0.1:18082/chat/all_messages', {
      channel_no: channelNo.value,
      clinic_no: clinicNo.value,
      copilot_type: copilotType.value,
      messages: transcription.value  // 将 transcription 的内容作为 message 传递
    }, {
      timeout: 60000,
      headers: headers,
    });

    console.log('Response:', response);
    if (response.data.code > 0) {
      ElMessageBox.alert(response.data.message, '提示', {
        confirmButtonText: '确定',
      });
      return
    }
    // 将返回的摘要存储到 summary 中
    const message = response.data.message || {}
    const copilot_id = message.copilot_id || 0;

    const finishData = {"type": "copilot", "type_id": copilot_id}
    await audioUploader.value.stopRecording(finishData)

    summary.value = message.answer || '摘要生成失败';
    const renderHtml = marked(summary.value);
    const plainText = replaceHtmlTagsWithNewlines(renderHtml)
    renderedSummary.value = plainText

  } catch (error) {
    console.error('生成摘要时出错:', error);
    summary.value = '生成摘要时发生错误';

    const finishData = {"type": "copilot", "type_id": 0}
    audioUploader.value.stopRecording(finishData).then(() => {
      console.log("Stop Recording Exception")
    })

  }
};

const handleExit = () => {
  if (typeof android !== 'undefined' && android && typeof android.onFinish === 'function') {
    // 调用 Android 原生方法
    android.onFinish();
  } else {
    // 如果不存在 android 对象，使用 router 进行跳转
    router.replace('/hrvtest');
  }
};

const loadVersion = async () => {
  try {
    const response = await fetch('version.json');
    if (response.ok) {
      const data = await response.json();
      version.value = data.version; // 更新版本号
    } else {
      console.error('无法加载版本号:', response.statusText);
    }
  } catch (error) {
    console.error('获取版本号时出错:', error);
  }
};

const replaceHtmlTagsWithNewlines = (htmlContent) => {
  return htmlContent
    // 替换 <br> 和 <br/> 为换行符
    .replace(/<br\s*\/?>/gi, '\n')
    // 替换 <p> 和 </p> 为换行符
    .replace(/<\/?p>/gi, '\n')
    // 替换 <ul> 和 </ul> 为换行符
    .replace(/<ul>/gi, '')
    // 替换 <ol> 和 </ol> 为换行符
    .replace(/<ol>/gi, '')
    // 替换 <li> 和 </li> 为换行符并添加缩进
    .replace(/<\/li>/gi, '\n')
    // 移除其他 HTML 标签
    .replace(/<[^>]*>/g, '');
}

onMounted(async () => {
  loadVersion();

  const urlParams = new URLSearchParams(window.location.search);
  const copilot_type = urlParams.get('copilot_type');
  if (copilot_type) {
    copilotType.value = copilot_type
  }

  updateSpeechToken()

  await nextTick(); // 确保DOM已完全渲染
});

onBeforeUnmount(() => {
});
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  padding: 10px;
  box-sizing: border-box;
  justify-content: space-between;
}

.header {
  text-align: center;
  color: #19191a;
  padding: 15px 0;
  border-radius: 10px;
}

.header h1 {
  font-family: 'STKaiti';
  font-weight: bold;
  font-size: 2rem;
  margin: 0;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* 添加文本阴影 */
}

.version {
  font-size: 1rem;
  color: #19191a;
  font-style: italic;
  margin-left: 10px;
  vertical-align: middle;
}

.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  justify-content: space-between;
}

.input-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  gap: 10px;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.input-container p {
  margin-bottom: 8px;
  color: #19191a;
  font-size: 16px; /* 增加字体大小 */
  font-weight: bold; /* 设置字体加粗 */
}

.custom-input {
  flex: 1;
  resize: none;
  color: #19191a;
}

::v-deep .el-textarea__inner {
  height: 100% !important;
  resize: none;
}

.summary-container {
  flex-grow: 1;
  color: #19191a;
  background-color: white;
  border-radius: 5px;
  padding: 10px;
  overflow-y: auto;
  box-sizing: border-box;
  white-space: pre-wrap;
  max-height: 100%;
}

.button-container {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  align-items: center;
  justify-content: center;
}

.custom-button {
  flex: 1;
  max-width: 200px;
  color: white;
  background-color: #17d7ea;
  border-color: #17d7ea;
}

.custom-button:hover {
  background-color: #14bccf; /* 在 hover 状态下的背景颜色，稍微深一点 */
  border-color: #14bccf; /* 在 hover 状态下的边框颜色 */
}

.custom-button:disabled {
  background-color: #b3eaf2 !important; /* 自定义禁用状态下的背景颜色 */
  border-color: #b3eaf2 !important; /* 自定义禁用状态下的边框颜色 */
  color: white !important; /* 保持文本颜色 */
  opacity: 0.6; /* 增加禁用状态的透明度 */
  cursor: not-allowed; /* 修改鼠标样式为不可点击 */
}

.profile-info {
  margin-bottom: 20px;
}

.profile-info label {
  display: inline-block;
  width: 80px;
  font-weight: bold;
  font-size: 16px;
}
.profile-info .el-input {
  width: 200px;
}
</style>
