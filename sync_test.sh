branch=$(git rev-parse --abbrev-ref HEAD)
commit=$(git rev-parse --short HEAD)
commit_count=$(git rev-list --count HEAD)
echo "generate version version=${branch}.${commit_count}.${commit}"
echo "{\"version\":\"${branch}.${commit_count}.${commit}\"}" > public/version.json

echo "build package"
yarn test

echo "sync workspace"
rsync -azP --exclude .DS_Store --delete dist/ root@123.56.239.79:/opt/ybbywb/frontend/tsc-llm-test/dist

echo "{\"version\":\"${branch}.${commit_count}.${commit}\"}  deployed"
