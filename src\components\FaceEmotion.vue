<template>
  <div class="face-emotion">
  
    <div class="section valence_arousal">
      <p class="valence-arousal-result emotion-result">{{$t('emotion_va_analyze_key')}}</p>
      <p class="valence">Valence: {{ result.valence_arousal.valence }} ({{ result.valence_arousal.valence_value }})</p>
      <p class="arousal">Arousal: {{ result.valence_arousal.arousal }} ({{ result.valence_arousal.arousal_value }})</p>
      <!-- <div class="valence-arousal-desc">
        <p>Valence：这个维度描述的是情绪的愉悦度或正负性。它可以是一个从负到正的连续体，其中一端代表不愉快或负面的情绪（如悲伤、恐惧），另一端代表愉快或正面的情绪（如快乐、兴奋）。</p>
        <p>Arousal：这个维度描述的是情绪的激活程度或强度。它可以是一个从低到高的连续体，其中一端代表平静或低唤醒度的情绪（如放松、平静），另一端代表高唤醒度的情绪（如兴奋、愤怒）。</p>
      </div> -->
      <div class="valence_arousal_img">
      <div class="image-container-va">
      <img :id="'image_' + index" src="https://llmagentstorage.blob.core.windows.net/static/ts/res/ValenceArousal.png" class="image-container-va-img" />
      <canvas :id="'overlay_' + index"></canvas>
      </div>
      </div>
    </div>

    <div class="section images">
      <div class="image-container">
        <img
            v-show="origImageLoaded"
            :src="result.orig_blob_url"
            alt="Original Image"
            class="image"
            @load="origImageLoaded = true"
        >
        <div v-show="!origImageLoaded" class="skeleton"></div>
      </div>
      <div class="image-container">
        <img
            v-show="meshImageLoaded"
            :src="result.mesh_blob_url"
            alt="Mesh Image"
            class="image"
            @load="meshImageLoaded = true"
        >
        <div v-show="!meshImageLoaded" class="skeleton"></div>
      </div>
    </div>

    <div class="section expression">
      <p class="emotion-result">{{$t('emotion_analyze_result_key')}}<span class="emotion-value">{{ translateEmotion(result.expression) }}</span></p>
      <div class="emotion-result-list au-grid">
        <div v-for="(value, key) in result.expression_list" :key="key" class="au-item">
          <span class="au-name">{{ translateEmotion(value.label) }}</span>
          <div class="progress-bar">
            <div class="progress" :style="{ width: `${value.probs * 100}%` }"></div>
          </div>
          <span class="percentage">{{ (value.probs * 100).toFixed(2) }}%</span>
        </div>
      </div>
    </div>

    <!-- <div class="section au-infos">
      <p class="emotion-result">Action Unit Information</p>
      <div class="au-grid">
        <div v-for="(value, key) in result.au_infos" :key="key" class="au-item">
          <span class="au-name">{{ key }}</span>
          <div class="progress-bar">
            <div class="progress" :style="{ width: `${value * 100}%` }"></div>
          </div>
          <span class="percentage">{{ (value * 100).toFixed(2) }}%</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { defineProps, ref, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  result: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const origImageLoaded = ref(false)
const meshImageLoaded = ref(false)

const translateEmotion = (emotion) => {
  const emotionMap = {
    'sadness': t('sad_key'),
    'happiness': t('happy_key'),
    'surprise': t('surprise_key'),
    'fear': t('fear_key'),
    'disgust': t('disgust_key'),
    'anger': t('anger_key'),
    'neutral': t('neutral_key'),
    'neglect': t('neglect_key')
  }
  const lowerCaseEmotion = emotion.toLowerCase()
  return emotionMap[lowerCaseEmotion] || emotion
}

const drawCrosshairOnImage = (imageId, canvasId, x, y) => {
  
  const image = document.getElementById(imageId);
  const canvas = document.getElementById(canvasId);
  
  image.onload = function() {
    // canvas.width = image.width - 365;
    // canvas.height = image.height - 120;
    canvas.width = image.width;
    canvas.height = image.height;
    
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    
    // const stepX = (x + 1) / 2 * countWidth;
    // const stepY = (y + 1) / 2 * countHeight;

    // const countWidth = canvas.width - 3;
    // const countHeight = canvas.height + 28;
    // const stepX = 182
    // const stepY = 72
    // const stepX = 652
    // const stepY = 538

    const stepX = x * ((652 - 182) / 2) + 182 + ((652 - 182) / 2)
    const stepY = y * ((538 - 72) / 2) + 72 + ((538 - 72) / 2)

    // ctx.beginPath();
    // ctx.moveTo(stepX, 0);
    // ctx.lineTo(stepX, canvas.height);
    // ctx.moveTo(0, stepY);
    // ctx.lineTo(canvas.width, stepY);
    // ctx.strokeStyle = 'red';
    // ctx.lineWidth = 2;
    // ctx.stroke();

    // 绘制原点标记
    ctx.beginPath();
    ctx.arc(stepX, stepY, 10, 0, 2 * Math.PI); // 绘制一个半径为10像素的圆
    ctx.fillStyle = 'red'; // 设置填充颜色
    ctx.fill(); // 填充圆
  };
  
  if (image.complete) {
    image.onload();
  }
}

onMounted(() => {
  console.log('Initial result object:', props.result)
  const imageId = 'image_' + props.index
  const canvasId = 'overlay_' + props.index
  
  const x = props.result.valence_arousal.valence_value
  const y = -props.result.valence_arousal.arousal_value
  drawCrosshairOnImage(imageId, canvasId, x, y);
  // window.addEventListener('resize',drawCrosshairOnImage);
})
onUnmounted(() => {
  // window.removeEventListener('resize', drawCrosshairOnImage);
});

watch(() => props.result, (newResult) => {
  console.log('Result object updated:', newResult)
}, { deep: true })

const logAUInfo = () => {
  console.log('Action Unit Information:')
  Object.entries(props.result.au_infos).forEach(([key, value]) => {
    console.log(`${key}: ${(value * 100).toFixed(2)}%`)
  })
}

onMounted(logAUInfo)
watch(() => props.result.au_infos, logAUInfo, { deep: true })
</script>

<style scoped>
.face-emotion {
  width: 100%;
  background: white;
}

.section {
  margin-bottom: 20px;
}

.images {
  display: flex;
  justify-content: space-between;
}

.image-container {
  width: 48%;
  position: relative;
}

.image {
  width: 100%;
  height: auto;
}

.skeleton {
  width: 100%;
  height: 0;
  padding-bottom: 75%;
  background-color: #e0e0e0;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.emotion-result {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  padding: 10px 0;
}

.emotion-value {
  color: #19191a;
}

.au-grid {
  display: grid;
  grid-template-columns: minmax(200px, auto) 1fr 80px;
  gap: 10px 15px;
  align-items: center;
}

.expression .au-grid {
  grid-template-columns: minmax(80px, auto) 1fr 80px;
}

.au-item {
  display: contents;
}

.au-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

.progress-bar {
  height: 20px;
  background-color: #e0e0e0;
  position: relative;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.5s ease-in-out;
  position: absolute;
  left: 0;
  top: 0;
}

.percentage {
  text-align: right;
  font-size: 14px;
  padding-left: 5px;
}

.valence-arousal-desc {
  color: gray;
  font-size: 11px;
  margin: 10px 0;
}

.valence_arousal_img {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container-va {
  /* position: relative; */
  display: inline-block;
}

.image-container-va-img {
  width: 100%;
  display: none;
}

.image-container-va canvas {
  /* position: absolute;
  top: 75px;
  left: 185px; */
}

</style>