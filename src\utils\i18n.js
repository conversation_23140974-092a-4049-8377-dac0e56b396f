import { getAndroidVersion } from "@/utils/android"
import { createI18n } from "vue-i18n"
import en from "../locales/en.js"
import zh from "../locales/zh.js"
import { getAppFirstReload, setAppFirstReload } from "./session-storage.js"

const messages = {
	"en-US": en,
	"zh-CN": zh,
}

const defaultLocale = "zh-CN"

function getInitialLocale() {
	const params = new URLSearchParams(window.location.search)
	const androidVersion = getAndroidVersion()
	let lang = "zh-CN"
	if (androidVersion >= 2 || window.localStorage != null) {
		lang =
			params.get("language") ||
			window.localStorage.getItem("language") ||
			defaultLocale
		lang = lang.includes("zh") ? "zh-CN" : "en-US"
		window.localStorage.setItem("language", lang)
	}
	return lang
}

const i18n = createI18n({
	locale: getInitialLocale(),
	fallbackLocale: defaultLocale,
	messages,
	legacy: false,
	// 可选：配置日期、数字等本地化选项
	// numberFormats: {
	//   'en-US': {
	//     currency: {
	//       style: 'currency', currency: 'USD'
	//     }
	//   },
	//   'zh-CN': {
	//     currency: {
	//       style: 'currency', currency: 'CNY'
	//     }
	//   }
	// },
	// 可选：配置日期时间本地化
	// datetimeFormats: {
	//   'en-US': {
	//     short: {
	//       year: 'numeric', month: 'short', day: 'numeric'
	//     }
	//   },
	//   'zh-CN': {
	//     short: {
	//       year: 'numeric', month: 'short', day: 'numeric'
	//     }
	//   }
	// }
})

export const changeLanguage = (lang) => {
	i18n.global.locale = lang
	const androidVersion = getAndroidVersion()
	window.localStorage.setItem("language", lang)
	if (!getAppFirstReload()) {
		setAppFirstReload(true)
		window.location.reload()
	}
}

export const getCurrentLanguage = () => {
	let lang = defaultLocale
	if (window.localStorage != null) {
		lang = window.localStorage.getItem("language") || defaultLocale
	}

	return lang
}

export default i18n
