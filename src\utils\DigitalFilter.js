// src/utils/DigitalFilter.js

class DigitalFilter {
  constructor(b, a) {
    this._bs = b;
    this._as = a;
    this._xs = new Array(b.length).fill(0);
    this._ys = new Array(a.length - 1).fill(0);
  }

  process(x) {
    if (isNaN(x)) { // ignore NaNs, and return as is
      return x;
    }

    this._xs.unshift(x);
    this._xs.pop();

    let y = (this.dotProduct(this._bs, this._xs) / this._as[0]) - this.dotProduct(this._as.slice(1), this._ys);

    this._ys.unshift(y);
    this._ys.pop();

    return y;
  }

  dotProduct(arr1, arr2) {
    return arr1.reduce((sum, val, idx) => sum + val * arr2[idx], 0);
  }
}

export default DigitalFilter;
