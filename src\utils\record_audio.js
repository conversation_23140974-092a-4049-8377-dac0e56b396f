import { api } from '@/api';
import axios from 'axios';

// 录制音频上传
export class AudioStreamUploader {
    constructor(config = {}) {
        this.config = {
            timeslice: 1000, // 每秒生成一个 Blob
            uploadUrl: '/upload/stream',
            ...config
        };
        
        this.mediaRecorder = null;
        this.record_id = 0;
        this.sequenceNumber = 0;
        this.isRecording = false;
        this.uploadQueue = new Queue();
        this.retryCount = new Map();
        this.stream = null
    }

    async startRecording() {
        try {

            // 初始化 MediaRecorder
            this.mediaRecorder = new MediaRecorder(this.stream, {
                mimeType: 'audio/webm'
            });

            // 创建录制会话
            // this.record_id = await this.initRecordingSession();
            // if (this.record_id == 0) {
            //     throw new Error("record_id gen failed")
            // }

            // 处理数据可用事件
            this.mediaRecorder.ondataavailable = async (event) => {
                if (event.data.size > 0) {
                    const chunk = {
                        data: event.data,
                        sequence: ++this.sequenceNumber,
                        timestamp: Date.now()
                    };
                    
                    await this.uploadQueue.enqueue(chunk);
                    this.processUploadQueue();
                }
            };

            // 开始录制
            this.isRecording = true;
            this.mediaRecorder.start(this.config.timeslice);
            
            // console.log('Recording started with ID:', this.record_id);
        } catch (error) {
            console.error('Failed to start recording:', error);
        }
    }

    async initRecordingSession() {
        let record_id = 0
        try {
            const response = await axios.post(`${this.config.uploadUrl}/init`, {
            });

            if (response.data.code !== 0) {
                throw new Error(response.data.message)
            }
            record_id = response.data.data.record_id
        } catch (error) {
            console.error('Session initialization failed:', error);
            record_id = 0
        }

        return record_id
    }

    async uploadChunk(chunk) {
        const formData = new FormData();
        // formData.append('record_id', this.record_id);
        formData.append('sequence', chunk.sequence);
        formData.append('timestamp', chunk.timestamp);
        formData.append('chunk', chunk.data);

        try {
            const response = await axios.post(this.config.uploadUrl + "/uploading", formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                },
            });

            if (response.status != 200) {
                throw new Error(`Upload failed with status: ${response.status}`);
            }
            if (response.data.code != 0) {
                throw new Error(`Upload failed with message: ${response.data.message}`);
            }

            console.log(`Chunk ${chunk.sequence} uploaded successfully`);
            return true;
        } catch (error) {
            console.log(error)
            const retries = this.retryCount.get(chunk.sequence) || 0;
            if (retries < 3) {
                this.retryCount.set(chunk.sequence, retries + 1);
                await this.uploadQueue.enqueue(chunk);
                console.log(`Chunk ${chunk.sequence} scheduled for retry`);
            } else {
                console.error(`Failed to upload chunk ${chunk.sequence} after 3 retries`);
            }
            return false;
        }
    }

    async processUploadQueue() {
        while (!this.uploadQueue.isEmpty()) {
            const chunk = await this.uploadQueue.dequeue();
            await this.uploadChunk(chunk);
        }
    }

    async stopRecording(finishData) {
        if (!this.isRecording) return;

        return new Promise(async (resolve, reject) => {
            try {
                this.mediaRecorder.onstop = async () => {
                    // 等待所有分片上传完成
                    await this.processUploadQueue();
                    
                    // 通知服务器录制结束
                    await this.finalizeRecording(finishData);
                    
                    this.isRecording = false;
                    resolve(this.record_id);
                };

                this.mediaRecorder.stop();
            } catch (error) {
                reject(error);
            }
        });
    }

    async finalizeRecording(finishData) {

        try {
            const response = await axios.post(`${this.config.uploadUrl}/finish`, {
                // record_id: this.record_id,
                total_chunks: this.sequenceNumber,
                data: finishData,
            });

            if (response.data.code !== 0) {
                throw new Error(response.data.message)
            }
            console.log('Recording finalized:', response.data);
        } catch (error) {
            console.error('Finalization failed:', error);
            throw error;
        }
    }
}

// 简单的队列实现
class Queue {
    constructor() {
        this.items = [];
    }

    async enqueue(item) {
        this.items.push(item);
    }

    async dequeue() {
        return this.items.shift();
    }

    isEmpty() {
        return this.items.length === 0;
    }
}
