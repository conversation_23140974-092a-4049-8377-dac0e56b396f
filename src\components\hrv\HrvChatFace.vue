<template>
	<div class="chart-container">
		<Line
			v-if="chartData.labels.length > 0"
			:data="chartData"
			:options="chartOptions"
		/>
	</div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, computed } from "vue"
import { Line } from "vue-chartjs"
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
} from 'chart.js';

ChartJS.register(
	Title,
	Tooltip,
	Legend,
	LineElement,
	CategoryScale,
	LinearScale,
	PointElement
)

const props = defineProps({
	timeDiffArray: {
		type: Array,
		required: true,
	},
	rgbMeanArray: {
		type: Array,
		required: true,
	},
})

const chartData = ref({
	labels: [],
	datasets: [
		{
			backgroundColor: "rgba(255, 99, 132, 0.2)",
			borderColor: "rgba(255, 99, 132, 1)",
			data: [],
			tension: 0.4,
			pointRadius: 0,
			pointHoverRadius: 0,
		},
		{
			backgroundColor: "rgba(75, 192, 192, 0.2)",
			borderColor: "rgba(75, 192, 192, 1)",
			data: [],
			tension: 0.4,
			pointRadius: 0,
			pointHoverRadius: 0,
		},
		{
			backgroundColor: "rgba(54, 162, 235, 0.2)",
			borderColor: "rgba(54, 162, 235, 1)",
			data: [],
			tension: 0.4,
			pointRadius: 0,
			pointHoverRadius: 0,
		},
	],
})

const yAxisMin = computed(() => {
	if (chartData.value.datasets[0].data.length === 0) return 0
	const allValues = chartData.value.datasets.flatMap(
		(dataset) => dataset.data
	)
	return Math.floor(Math.min(...allValues) * 0.9)
})

const yAxisMax = computed(() => {
	if (chartData.value.datasets[0].data.length === 0) return 255
	const allValues = chartData.value.datasets.flatMap(
		(dataset) => dataset.data
	)
	return Math.ceil(Math.max(...allValues) * 1.1)
})

const chartOptions = ref({
	responsive: true,
	animation: false,
	maintainAspectRatio: false,
	scales: {
		y: {
			min: yAxisMin.value,
			max: yAxisMax.value,
			title: {
				display: false,
			},
		},
		x: {
			type: "linear",
			min: 0,
			max: 10000,
			title: {
				display: false,
			},
			ticks: {
				stepSize: 1000,
				callback: function (value) {
					return value / 1000
				},
			},
		},
	},
	plugins: {
		legend: {
			display: false,
		},
		title: {
			display: false,
		},
	},
	elements: {
		point: {
			radius: 0,
		},
		line: {
			borderWidth: 2,
		},
	},
})

const updateChartData = () => {
	// 1. 截取最后10秒的数据
	const totalTime = props.timeDiffArray.reduce((sum, curr) => sum + curr, 0)
	let cutoffIndex = props.timeDiffArray.length - 1
	let cutoffTime = 0

	while (cutoffIndex >= 0 && cutoffTime < 10000) {
		cutoffTime += props.timeDiffArray[cutoffIndex]
		cutoffIndex--
	}

	const slicedTimeDiff = props.timeDiffArray.slice(cutoffIndex + 2)
	const slicedRgbMean = props.rgbMeanArray.slice(cutoffIndex + 2)

	// 2. 将截取的timeDiffArray映射到累计时间
	const accumulatedTime = []
	let sum = 0
	slicedTimeDiff.forEach((diff) => {
		sum += diff
		accumulatedTime.push(sum)
	})

	// 3. 更新图表数据
	chartData.value = {
		labels: accumulatedTime,
		datasets: [
			{
				...chartData.value.datasets[0],
				data: slicedRgbMean.map((rgba, index) => ({
					x: accumulatedTime[index],
					y: rgba[0],
				})),
			},
			{
				...chartData.value.datasets[1],
				data: slicedRgbMean.map((rgba, index) => ({
					x: accumulatedTime[index],
					y: rgba[1],
				})),
			},
			{
				...chartData.value.datasets[2],
				data: slicedRgbMean.map((rgba, index) => ({
					x: accumulatedTime[index],
					y: rgba[2],
				})),
			},
		],
	}

	// 4. 强制更新
	nextTick(() => {
		if (chartInstance.value) {
			chartInstance.value.update()
		}
	})
}

// 监听 props 的变化
watch(
	[() => props.timeDiffArray, () => props.rgbMeanArray],
	() => {
		updateChartData()
	},
	{ deep: true }
)

const chartInstance = ref(null)

onMounted(() => {
	updateChartData()
})

// 提供一个方法来获取图表实例
const setChartInstance = (chart) => {
	chartInstance.value = chart
}
</script>

<style scoped>
.chart-container {
	width: 100%;
}
</style>
