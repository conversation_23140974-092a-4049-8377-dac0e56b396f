function getAndroidVersion() {
	if (
		typeof android !== "undefined" &&
		typeof android.getVersionCode == "function"
	) {
		const versionCode = android.getVersionCode()
		console.log("getAndroidVersion: " + versionCode)
		return versionCode
	}
	return 0
}
const notifyAndroidReady = () => {
	if (isAndroidEnv()) {
		console.log("android next")
		// 用于右边webview放大，通知android端
		android.ready()
	}
}
const isAndroidEnv = () => {
	try {
		return typeof android !== "undefined" && android
	} catch (error) {
		console.log("🚀 ~ isAndroidEnv ~ error:", error)
	}
}
const goBackAppHomePage = () => {
	if (isAndroidEnv()) {
		// 跳转到android扫码页面
		android.goHome()
	}
}
const notifyPrintPdf = () => {
	// 调用android注入的接口
	if (isAndroidEnv()) {
		android.printPage()
	} else {
		window.print() // 降级方案：使用浏览器默认打印
	}
}
const notifyAndroidOnFinish = () => {
	if (isAndroidEnv() && android.onFinish) {
		// 跳转到android选择项目页面
		android.onFinish()
		return true
	}
	return false
}

const startEyeTracking = () => {
	if (isAndroidEnv() && android.startGazeTracking) {
		console.log("请求启动眼动追踪")
		android.startGazeTracking()
	}
}
const stopEyeTracking = () => {
	if (isAndroidEnv() && android.stopGazeTracking) {
		console.log("请求关闭眼动追踪")
		android.stopGazeTracking()
	}
}
const getAndroidInitParams = () => {
	if (isAndroidEnv() && android.getInitialParams) {
		return android.getInitialParams()
			? JSON.parse(android.getInitialParams())
			: null
	}
}
export {
	getAndroidInitParams,
	getAndroidVersion,
	isAndroidEnv,
	goBackAppHomePage,
	notifyAndroidReady,
	notifyPrintPdf,
	notifyAndroidOnFinish,
	startEyeTracking,
	stopEyeTracking,
}
