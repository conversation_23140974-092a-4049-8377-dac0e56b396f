<template>
	<div class="container health_mtrics_info">
		<h1 class="title">{{ t("index_interpretation_report_key") }}</h1>
		<div class="content section-container">
			<div
				v-for="metric in healthMetrics"
				:key="metric.key"
				class="interpretation"
			>
				<div class="pressure-card" v-if="healthMetricsInfo[metric.key]">
					<h2>{{ metric.name }}</h2>
					<div class="definition">
						{{ metric.name }}:
						{{ healthMetricsInfo[metric.key].definition }}
					</div>
					<div class="score">
						{{ t("hrv_report_score") }}:
						{{ healthMetricsInfo[metric.key].value }}
					</div>
					<div class="score-description">
						{{ healthMetricsInfo[metric.key].description }}
					</div>
					<div class="description">
						{{ healthMetricsInfo[metric.key].descDetail }}
					</div>
					<div class="recommendations-text">
						{{ t("suggestion") }}
					</div>
					<ul class="recommendations">
						<li
							v-for="(suggestion, index) in healthMetricsInfo[
								metric.key
							].suggestion || []"
							:key="index"
							class="suggetion-unit"
						>
							{{ suggestion }}
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from "vue"
import { useI18n } from "vue-i18n"

const healthMetricsInfo = ref({})

const { t } = useI18n()

const healthMetrics = [
	{ name: t("stress_resistance_key"), key: "stressResistance", value: 60 },
	{ name: t("mental_pressure_key"), key: "mentalPressure", value: 40 },
	{ name: t("physical_pressure_key"), key: "physicalPressure", value: 45 },
	{ name: t("balance_autonomic_nervous_key"), key: "balance", value: 65 },
	{ name: t("activity_autonomic_nervous_key"), key: "activity", value: 55 },
]

const props = defineProps({
	PressureInfo: {
		type: Object,
		required: true,
	},
	NerveInfo: {
		type: Object,
		required: true,
	},
})

const interpretations = {
	balance: t("balance_detail_key"),
	activity: t("activity_detail_key"),
	physicalPressure: t("physical_pressure_detail_key"),
	mentalPressure: t("mental_pressure_detail_key"),
	stressResistance: t("stress_resistance_detail_key"),
}

const formatMetricsInfo = () => {
	let tmpHealthMetricsInfo = {}
	for (let key in props.PressureInfo) {
		if (props.PressureInfo.hasOwnProperty(key)) {
			let info = props.PressureInfo[key]
			tmpHealthMetricsInfo[key] = info
		}
	}
	for (let key in props.NerveInfo) {
		if (props.NerveInfo.hasOwnProperty(key)) {
			let info = props.NerveInfo[key]
			tmpHealthMetricsInfo[key] = info
		}
	}
	healthMetricsInfo.value = tmpHealthMetricsInfo
}

const formatMarkdown = (markdown) => {
	return markdown
		.replace(
			/^# (.*$)/gm,
			'<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>'
		)
		.replace(
			/^## (.*$)/gm,
			'<h2 class="text-xl font-semibold mt-3 mb-1">$1</h2>'
		)
		.replace(
			/^### (.*$)/gm,
			'<h3 class="text-lg font-semibold mt-2 mb-1">$1</h3>'
		)
		.replace(/^\* (.*$)/gm, '<li class="ml-4">$1</li>')
		.replace(/^(?!\<h|\<li)(.*$)/gm, '<p class="my-1">$1</p>')
}

onMounted(() => {
	formatMetricsInfo()
})

watch(
	() => props.PressureInfo,
	() => {
		formatMetricsInfo()
	}
)
</script>

<style scoped>
.container {
	max-width: 1200px;
	margin: 0 auto;
	/* padding: 1rem; */
}

.title {
	font-size: 1.5rem;
	font-weight: bold;
	margin-bottom: 1rem;
}

.content {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.subtitle {
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 0.5rem;
}

.alert {
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	padding: 1rem;
	margin-bottom: 1rem;
}

.alert-description :deep(h1) {
	font-size: 1.5rem;
	font-weight: bold;
	margin-top: 1rem;
	margin-bottom: 0.5rem;
}

.alert-description :deep(h2) {
	font-size: 1.25rem;
	font-weight: semibold;
	margin-top: 0.75rem;
	margin-bottom: 0.25rem;
}

.alert-description :deep(h3) {
	font-size: 1.1rem;
	font-weight: semibold;
	margin-top: 0.5rem;
	margin-bottom: 0.25rem;
}

.alert-description :deep(ul) {
	list-style-type: disc;
	padding-left: 1.5rem;
}

.alert-description :deep(li) {
	margin-bottom: 0.25rem;
}

.health_mtrics_info .section-container {
	display: flex;
	flex-wrap: wrap;
	gap: 60px;
	justify-content: center;
}

.health_mtrics_info .pressure-card {
	background: white;
	/* border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1); */
	border: 1px solid #ddd;
	padding: 25px;
	width: 100%;
	margin-bottom: 20px;
}

.health_mtrics_info h2 {
	color: #2c3e50;
	border-bottom: 3px solid rgb(23, 215, 226);
	padding-bottom: 10px;
	margin-bottom: 20px;
}

.health_mtrics_info .score {
	font-size: 24px;
	font-weight: bold;
	/* color: rgb(23, 215, 226); */
	margin: 15px 0;
}

.health_mtrics_info .score-description {
	font-weight: 800;
}

.health_mtrics_info .definition {
	color: #7f8c8d;
	font-style: italic;
}

.health_mtrics_info .description {
	/* background: #f8f9fa;
    padding: 15px;
    border-radius: 5px; */
	margin: 15px 0;
	font-weight: 800;
}

.health_mtrics_info .recommendations-text {
	font-size: 24px;
	padding-left: 0;
	/* color: #27ae60; */
}

.health_mtrics_info .recommendations {
	list-style-type: square;
	padding-left: 20px;
	/* color: #27ae60; */
}

.health_mtrics_info .recommendations li {
	padding: 5px 0;
}

@media (max-width: 768px) {
	.health_mtrics_info .pressure-card {
		width: 100%;
	}
}

@media print {
	.health_mtrics_info {
		/* display: none; */
  		page-break-before: always;
		
	}
	.interpretation {
		page-break-inside: avoid;
	}
	.next-page {
		page-break-before: auto;
  		page-break-after: always;
	}
}
</style>
