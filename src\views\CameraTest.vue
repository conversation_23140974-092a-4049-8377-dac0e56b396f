<template>
  <div>
    <h1>摄像头测试</h1>
    <video ref="video" autoplay playsinline></video>
    <p v-if="error">{{ error }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      error: null
    };
  },
  mounted() {
    this.startCamera();
  },
  methods: {
    async startCamera() {
      try {
        // 请求摄像头权限，并获取前置摄像头的流
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: "user"
          }
        });
        // 将视频流设置到video元素
        this.$refs.video.srcObject = stream;
      } catch (err) {
        this.error = '无法访问摄像头: ' + err.message;
      }
    }
  }
};
</script>

<style scoped>
video {
  width: 100%;
  height: auto;
}
</style>
