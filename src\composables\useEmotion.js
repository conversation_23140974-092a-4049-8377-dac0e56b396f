import { ref } from 'vue';
import { api } from '@/api';

export function useEmotion() {
  const emotionResult = ref(null);
  const error = ref(null);
  const capturedImageData = ref(null);
  const enSessionId = ref("")
  const isHiddenReport = ref(0)

  const sendFrameToBackend = async (timestamp, user_id, session_id, imageData, device_id) => {
    try {
      const fullUrl = `${api.defaults.baseURL}/api/v1/screening/emotion`;
      console.log('Sending request to:', fullUrl);

      const requestBody = {
        timestamp: timestamp,
        user_id: user_id,
        session_id: session_id,
        image: imageData
      };

      const header_info = {
        headers: {
          'DEVICE-ID': device_id,
          'TS-Auth-Token': window.localStorage.getItem('user_token')
        }
      }

      const safeLogBody = {
        ...requestBody,
        image: `[Base64 Image Data] Length: ${imageData.length}`
      };
      console.log('Request body:', JSON.stringify(safeLogBody, null, 2));

      const response = await api.post('/api/v1/screening/emotion', requestBody, header_info);

      console.log('Response status:', response.status);
      console.log('Response data:', JSON.stringify(response.data, null, 2));

      emotionResult.value = response.data.data.emotion_result;
      enSessionId.value = response.data.data.en_session_id;
      isHiddenReport.value = response.data.data.is_hidden_report;

      capturedImageData.value = imageData;  // Store the image data
      error.value = null;
    } catch (err) {
      console.error('Error sending frame to backend:', err);
      if (err.response) {
        console.log('Error response:', err.response.status, err.response.data);
      }
      error.value = 'Failed to process emotion. Please try again.';
      capturedImageData.value = null;  // Clear the image data on error
    }
  };

  return {
    emotionResult,
    enSessionId,
    isHiddenReport,
    error,
    capturedImageData,
    sendFrameToBackend
  };
}