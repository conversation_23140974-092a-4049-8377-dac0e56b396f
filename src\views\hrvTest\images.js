import { writeFileSync, readdirSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

function getImageFiles(directoryPath) {
  try {
    const files = readdirSync(directoryPath)
    const imageFiles = files.filter(file => 
      file.toLowerCase().endsWith('.jpg') || 
      file.toLowerCase().endsWith('.jpeg')
    )
    return imageFiles
  } catch (error) {
    console.error('Error reading directory:', error)
    return []
  }
}

const imageFiles = getImageFiles(
  join(__dirname, '../../../public/image_new')
)

// 将数组保存到file.js
const outputPath = join(__dirname, 'file.js')
const fileContent = `const imageFiles = ${JSON.stringify(imageFiles, null, 2)}\nexport { imageFiles }`

writeFileSync(outputPath, fileContent)
console.log('图片列表已保存到 file.js')