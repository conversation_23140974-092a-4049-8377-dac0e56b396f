<template>
	<table v-if="Object.keys(PressureInfo).length > 0">
		<thead>
			<tr>
				<th colspan="3" class="chart-header">
					{{ $t("stress_evaluation_key") }}
				</th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<!-- 按照指定顺序渲染 -->
				<td
					v-for="(item, index) in pressureKeyList"
					:key="index"
					class="chart-cell"
				>
					<PressureChart
						:index="item"
            :compareDescription="PressureInfo[item].rank"
						:title="PressureInfo[item].title"
						:value="PressureInfo[item].value"
						:barMax="PressureInfo[item].max"
						:barMin="PressureInfo[item].min"
						:barValue="PressureInfo[item].value"
						:description="PressureInfo[item].description"
					/>
				</td>
			</tr>
		</tbody>
	</table>
</template>

<script setup>
// Import the PressureChart component
import PressureChart from "./PressureChart.vue"

// Define the props to accept PressureInfo object
const pressureKeyList = [
	"stressResistance",
	"mentalPressure",
	"physicalPressure",
]
const props = defineProps({
	PressureInfo: {
		type: Object,
		required: true,
	},
})
</script>

<style scoped>
table {
	width: 100%;
	border-collapse: collapse;
	text-align: center;
}

th,
td {
	padding: 8px;
	border: 1px solid #ddd;
}

.chart-cell {
	width: 33.33%; /* Each chart takes one-third of the row */
	vertical-align: top; /* Align content to the top */
}

.chart-header {
	text-align: left !important; /* 强制左对齐 */
	background-color: #17d7e2; /* 深绿色背景 */
	color: white;
	font-weight: bold;
}

@media print {
	table {
		margin-bottom: 0px !important;
	}
}
</style>
