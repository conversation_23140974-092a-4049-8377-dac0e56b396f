import { createRouter, createWebHistory } from 'vue-router';

const Copilot = () => import('../views/Copilot.vue');
const HrvTest = () => import('../views/hrvTest/index.vue');
const HrvInit = () => import('../views/hrvInit/index.vue');
const HrvReport = () => import('../views/hrvReport/index.vue');
const HrvFinished = () => import('../views/hrvFinished/index.vue');
const Emotion = () => import('../views/Emotion.vue');
const EmotionReport = () => import('../views/EmotionReport.vue');
import MRAssistant from '../views/MRAssistant.vue';
// import HrvTest2 from '@/views/HrvTest2.vue';
const SpeechReport = () => import('../views/SpeechReport.vue');
const SpeechTest = () => import('../views/SpeechTest.vue');

const routes = [
  {
    path: '/',
    redirect: '/mrassistant'
  },
  {
    path: '/copilot',
    name: 'Copilot',
    component: Copilot,
  },
  {
    path: '/hrvinit',
    name: 'HrvInit',
    component: HrvInit,
    meta: {
      title: '心理压力测试 - 主页'
    }
  },
  {
    path: '/hrvtest',
    name: 'HrvTest',
    component: HrvTest,
    meta: {
      title: '心理压力测试 - 测试'
    }
  },
  // {
  //   path: '/hrvtest2',
  //   name: 'HrvTest2',
  //   component: HrvTest2,
  //   meta: {
  //     title: '心理压力测试 - 测试'
  //   }
  // },
  {
    path: '/hrvreport',
    name: 'HrvReport',
    component: HrvReport,
    props: (route) => ({
      // basicInfo: JSON.parse(route.query.basicInfo || '{}'),
      // RRIntervals: JSON.parse(route.query.RRIntervals || '[]'),
      // timeDomainMetrics: JSON.parse(route.query.timeDomainMetrics || '{}'),
      // frequencyDomainMetrics: JSON.parse(route.query.frequencyDomainMetrics || '{}'),
      // nerveInfo: JSON.parse(route.query.nerveInfo || '{}'),
      // pressureInfo: JSON.parse(route.query.pressureInfo || '{}'),
      reportId: route.query.reportId || 0,
    }),
    meta: {
      title: '心理压力测试 - 报告'
    }
  },
  {
    path: '/hrvfinished',
    name: 'HrvFinished',
    component: HrvFinished,
    meta: {
      title: '心理压力测试 - 完成'
    }
  },
  {
    path: '/mrassistant',
    name: 'MRassistant',
    component: MRAssistant,
    meta: {
      title: '病历助手'
    }
  },
  {
    path: '/emotion',
    name: 'Emotion',
    component: Emotion,
    meta: {
      title: '情绪检测'
    }
  },
  {
    path: '/emotion_report',
    name: 'EmotionReport',
    component: EmotionReport,
    props: (route) => ({
      en_session_id: route.query.en_session_id || "",
    }),
  },
  {
    path: '/speech',
    name: 'Speech',
    component: SpeechTest,
    meta: {
      title: '语音情绪检测'
    }
  },
  {
    path: '/speech_report',
    name: 'SpeechReport',
    component: SpeechReport,
    props: (route) => ({
      en_screening_id: route.query.en_screening_id || "",
    }),
  }
  // 其他路由配置...
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
