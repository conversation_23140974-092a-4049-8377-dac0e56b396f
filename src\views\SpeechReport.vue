<template>
    <div class="speech-report">
      <h1>{{$t('speech_report_title_key')}}</h1>
      <div v-if="hasResults">
  
        <div class="basic-info">
          <div>
            <span>{{$t('evaluation_date_key')}} {{$t('colon_key')}}</span>
            <span class="emphasis">{{ screen_time }}</span>
          </div>
          <div>
            <span>{{$t('evaluation_method_key')}} {{$t('colon_key')}}</span>
            <span class="emphasis">{{$t('emotion_sys_ai_key')}}</span>
          </div>
          <div>
            <span>{{$t('report_number_key')}} {{$t('colon_key')}}</span>
            <span class="emphasis">{{ screen_idx }}</span>
          </div>
        </div>
  
        <div class="section">
            <h2>{{$t('speech_report_section1_key')}}</h2>
            <p>{{$t('dear_key')}} {{ screen_name }} {{$t('colon_key')}}</p>
            <p>{{$t('speech_report_section1_key_desc')}} {{ total_status.text }}</p>

            <div id="emotion-chart" class="chart-container">
                <div class="emotion-result-list au-grid">
                    <div v-for="(value, key) in emotions" :key="key" class="au-item">
                        <span class="au-name">{{ translateEmotion(key) }}</span>
                        <div class="progress-bar">
                            <div class="progress" :style="{ width: `${value * 100}%` }"></div>
                        </div>
                        <span class="percentage">{{ (value * 100).toFixed(2) }}%</span>
                    </div>
                </div>
            </div>

            <!-- 波形显示区域 -->
            <AudioPlayer :audioUrl="audio_url" />

        </div>

        <div class="section speech-analysis">
            <h2>{{$t('speech_report_section2_key')}}</h2>
            
            <h3>{{$t('speech_report_section2_key_1')}}</h3>
            <div class="data-grid">
                <div class="data-card">
                    <h4>{{$t('speech_report_section2_key_1_h1')}} {{ positive_emotion.percent }}%)</h4>
                    <ul v-for="(result, index) in positive_emotion.detail" :key="index">
                        <li>{{ result.text }} ({{ result.percent }}%)
                            <ul v-for="(result_tip, index_tip) in result.tips" :key="index_tip">
                                <li>{{ result_tip }}</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div v-if="neutral_emotion.detail" class="data-card">
                    <h4>{{$t('speech_report_section2_key_1_h2')}} ({{ neutral_emotion.percent }}%)</h4>
                    <ul v-for="(result, index) in neutral_emotion.detail[0].tips" :key="index">
                        <li>{{ result }}</li>
                    </ul>
                </div>

                <div class="data-card">
                    <h4>{{$t('speech_report_section2_key_1_h3')}} {{ negative_emotion.percent }}%)</h4>
                    <ul v-for="(result, index) in negative_emotion.detail" :key="index">
                        <li>{{ result.text }} ({{ result.percent }}%)
                            <ul v-for="(result_tip, index_tip) in result.tips" :key="index_tip">
                                <li>{{ result_tip }}</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <h3>{{$t('speech_report_section2_key_2')}}</h3>
            <div class="data-grid">
                <div v-if="arousal_result.arousal" class="data-card">
                    <h4>{{$t('speech_report_section2_key_2_h1')}} (Arousal {{ (arousal_result.arousal).toFixed(2) }})</h4>
                    <ul>
                        <li>{{ arousal_result.arousal_text }}</li>
                        <li v-if="arousal_result.first_text">{{ arousal_result.first_text }}</li>
                        <li v-if="arousal_result.second_text">{{ arousal_result.second_text }}</li>
                    </ul>
                </div>

                <div v-if="valence_result.valence" class="data-card">
                    <h4>{{$t('speech_report_section2_key_2_h2')}} (Valence {{ (valence_result.valence).toFixed(2) }})</h4>
                    <ul>
                        <li>{{ valence_result.valence_text }}</li>
                        <li v-if="valence_result.first_text">{{ valence_result.first_text }}</li>
                        <li v-if="valence_result.second_text">{{ valence_result.second_text }}</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="section speech-sugguestion">
            <h2>{{$t('speech_report_section3_key')}}</h2>
            
            <div class="data-card">
                <h3>{{$t('speech_report_section3_key_1')}}</h3>
                
                <h4>{{$t('speech_report_section3_key_1_a')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section3_key_1_a_li1')}}</li>
                    <li>{{$t('speech_report_section3_key_1_a_li2')}}</li>
                    <li>{{$t('speech_report_section3_key_1_a_li3')}}</li>
                    <li>{{$t('speech_report_section3_key_1_a_li4')}}</li>
                </ul>

                <h4>{{$t('speech_report_section3_key_1_b')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section3_key_1_b_li1')}}</li>
                    <li>{{$t('speech_report_section3_key_1_b_li2')}}</li>
                    <li>{{$t('speech_report_section3_key_1_b_li3')}}</li>
                    <li>{{$t('speech_report_section3_key_1_b_li4')}}</li>
                </ul>
            </div>

            <div class="data-card">
                <h3>{{$t('speech_report_section3_key_2')}}</h3>
                
                <h4>{{$t('speech_report_section3_key_2_a')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section3_key_2_a_li1')}}</li>
                    <li>{{$t('speech_report_section3_key_2_a_li2')}}</li>
                    <li>{{$t('speech_report_section3_key_2_a_li3')}}</li>
                    <li>{{$t('speech_report_section3_key_2_a_li4')}}</li>
                </ul>

                <h4>{{$t('speech_report_section3_key_2_b')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section3_key_2_b_li1')}}</li>
                    <li>{{$t('speech_report_section3_key_2_b_li2')}}</li>
                    <li>{{$t('speech_report_section3_key_2_b_li3')}}</li>
                    <li>{{$t('speech_report_section3_key_2_b_li4')}}</li>
                </ul>
            </div>
        </div>

        <div class="section speech-future-plan">
            <h2>{{$t('speech_report_section4_key')}}</h2>

            <div class="data-card">
                <h3>{{$t('speech_report_section4_key_1')}}</h3>
                
                <h4>{{$t('speech_report_section4_key_1_a')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section4_key_1_a_li1')}}</li>
                    <li>{{$t('speech_report_section4_key_1_a_li2')}}</li>
                    <li>{{$t('speech_report_section4_key_1_a_li3')}}</li>
                    <li>{{$t('speech_report_section4_key_1_a_li4')}}</li>
                </ul>

                <h4>{{$t('speech_report_section4_key_1_b')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section4_key_1_b_li1')}}</li>
                    <li>{{$t('speech_report_section4_key_1_b_li2')}}</li>
                    <li>{{$t('speech_report_section4_key_1_b_li3')}}</li>
                    <li>{{$t('speech_report_section4_key_1_b_li4')}}</li>
                </ul>
            </div>

            <div class="data-card">
                <h3>{{$t('speech_report_section4_key_2')}}</h3>
                
                <h4>{{$t('speech_report_section4_key_2_a')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section4_key_2_a_li1')}}</li>
                    <li>{{$t('speech_report_section4_key_2_a_li2')}}</li>
                    <li>{{$t('speech_report_section4_key_2_a_li3')}}</li>
                    <li>{{$t('speech_report_section4_key_2_a_li4')}}</li>
                </ul>

                <h4>{{$t('speech_report_section4_key_2_b')}}</h4>
                <ul>
                    <li>{{$t('speech_report_section4_key_2_b_li1')}}</li>
                    <li>{{$t('speech_report_section4_key_2_b_li2')}}</li>
                    <li>{{$t('speech_report_section4_key_2_b_li3')}}</li>
                    <li>{{$t('speech_report_section4_key_2_b_li4')}}</li>
                </ul>
            </div>
        </div>

        <div class="section speech-tips">
            <h2>{{$t('speech_report_section5_key')}}</h2>
            <div class="notice">
                <ul>
                    <li>{{$t('speech_report_section5_key_li1')}}</li>
                    <li>{{$t('speech_report_section5_key_li2')}}</li>
                    <li>{{$t('speech_report_section5_key_li3')}}</li>
                    <li>{{$t('speech_report_section5_key_li4')}}</li>
                </ul>
            </div>
        </div>

        <div class="section speech-reference">
            <h2>{{$t('speech_report_section6_key')}}</h2>
            <div class="reference">
                <p>1. Scherer, K. R. (2019). "Speech Emotion Recognition: Enhancing Robustness and Cross-Corpus Generalisability." Journal of Acoustic Society.</p>
                <p class="reference-note">- {{$t('speech_report_section6_key_p1')}}</p>
            </div>
            
            <div class="reference">
                <p>2. Schuller, B. W. (2020). "Speech Emotion Recognition: Two Decades in a Nutshell, Benchmarks, and Ongoing Trends." Communications of the ACM.</p>
                <p class="reference-note">- {{$t('speech_report_section6_key_p2')}}</p>
            </div>
            
            <div class="reference">
                <p>3. Kory, J., & D'Mello, S. K. (2018). "Affect Recognition from Speech: A Review." IEEE Transactions on Affective Computing.</p>
                <p class="reference-note">- {{$t('speech_report_section6_key_p3')}}</p>
            </div>
        </div>

        <div class="section emergency-contacts">
            <h2>{{$t('speech_report_section7_key')}}</h2>
            <div class="notice">
                <ul>
                    <li>{{$t('speech_report_section7_key_li1')}}</li>
                    <li>{{$t('speech_report_section7_key_li2')}}</li>
                    <li>{{$t('speech_report_section7_key_li3')}}</li>
                </ul>
            </div>
        </div>

        <div class="section footer">
            <div class="notes">
                <p>{{$t('emotion_report_notes_key')}}</p>
                <ol>
                    <li>{{$t('emotion_report_notes_key_li1')}}</li>
                    <li>{{$t('emotion_report_notes_key_li2')}}</li>
                    <li>{{$t('emotion_report_notes_key_li3')}}</li>
                </ol>
            </div>

            <div class="signature">
                <p>{{$t('report_doctor_key')}} {{$t('colon_key')}}</p>
                <p>{{$t('report_date_key')}} {{$t('colon_key')}}</p>
                <p>{{$t('report_hospital_seal')}} {{$t('colon_key')}}</p>
            </div>
        </div>

      </div>
      <div v-else>
        No results available. Please perform an analysis first.
      </div>
      <div class="button-container">
        <button @click="exportToPDF" class="back-btn">{{$t('print_report_key')}}</button>
        <button @click="goBack" class="back-btn">{{$t('return_key')}}</button>
      </div>
    </div>
  </template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSpeechStore } from '@/stores/speechStore'
import { storeToRefs } from 'pinia'
import AudioPlayer from '@/components/AudioPlayer.vue'
import { useI18n } from 'vue-i18n';
import { api } from '@/api';

const { t } = useI18n();

const router = useRouter()
const speechStore = useSpeechStore()
const { analysisResults, hasResults } = storeToRefs(speechStore)

const screen_idx = ref("")
const screen_time = ref("")
const screen_name = ref("")
const audio_url = ref("")
const emotions = ref({})
const total_status = ref({})
const positive_emotion = ref({})
const neutral_emotion = ref({})
const negative_emotion = ref({})
const arousal_result = ref({})
const valence_result = ref({})

const translateEmotion = (emotion) => {
  const emotionMap = {
    'sad': t('sad_key'),
    'happy': t('happy_key'),
    'surprised': t('surprised_happy_key'),
    'fear': t('fear_key'),
    'disgust': t('disgust_key'),
    'angry': t('anger_key'),
    'neutral': t('neutral_key')
  }
  const lowerCaseEmotion = emotion.toLowerCase()
  return emotionMap[lowerCaseEmotion] || emotion
}

const props = defineProps({
    en_screening_id: String,
});

const goBack = () => {
    speechStore.resetState()
    router.push({ name: 'Speech' })
}

const exportToPDF = () => {
  // 调用android注入的接口
  if (typeof android !== 'undefined') {
      android.printPage();
  } else {
      window.print(); // 降级方案：使用浏览器默认打印
  }
}

const getSpeechData = async () => {

    try {
    const response = await api.get('/api/v1/screening/get_speech_report/' + props.en_screening_id);

    if (response.status != 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = response.data;

    if (data.code !== 0) {
        throw new Error(`API error! code: ${data.code}`);
    }

    return data.data.speech_info;
    } catch (error) {
    console.error('There was a problem with the fetch operation:', error);
    throw error;
    }
}

onMounted( async () => {

    analysisResults.value = await getSpeechData()
    hasResults.value = analysisResults.value.length > 0

    // console.log(analysisResults.value)
    audio_url.value = analysisResults.value.audio_url
    emotions.value = analysisResults.value.emotions
    screen_idx.value = analysisResults.value.screening_idx
    screen_time.value = analysisResults.value.screening_time
    total_status.value = analysisResults.value.total_status
    positive_emotion.value = analysisResults.value.positive_emotion
    neutral_emotion.value = analysisResults.value.neutral_emotion
    negative_emotion.value = analysisResults.value.negative_emotion
    arousal_result.value = analysisResults.value.arousal_result
    valence_result.value = analysisResults.value.valence_result
})
watch(analysisResults, { deep: true })
</script>

<style scoped>

.speech-report {
    max-width: 900px;
    width: 100vw;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    color: #333;
    font-size: 1.2rem;
  }
  
  .chart-container {
    margin: 1.8rem 0;
  }

  /* 标题样式 */
  h1 {
      font-size: 2.4rem;
      font-weight: 300;
      letter-spacing: 0.1em;
      margin-bottom: 2rem;
      text-align: center;
      padding-bottom: 2rem;
      border-bottom: 1px solid #e6e6e6;
      color: #333;
      margin-bottom: 30px;
  }
  
  h2 {
      font-size: 1.8rem;
      font-weight: 300;
      letter-spacing: 0.05em;
      color: #666;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 10px;
      margin-bottom: 20px;
  }
  
  h3 {
      font-size: 1.4rem;
      font-weight: 400;
      margin: 2rem 0;
      color: #707070;
  }
  
  h4 {
      font-size: 1.2rem;
      font-weight: 400;
      margin: calc(2rem * 0.5) 0;
      color: #707070;
  }
  
  p {
      margin-bottom: 1rem;
      line-height: 1.8;
  }
  
  .button-container {
    display: flex;
    justify-content: center;
    margin-top: auto;
    padding-top: 20px;
    padding-bottom: 20px;
    gap: 10px;
  }
  
  .back-btn {
    min-width: 120px; /* 设置最小宽度 */
    padding: 12px 24px;
    font-size: 16px;
    background-color: #17d7ea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  
  .back-btn:hover {
    background-color: #14bccf;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .back-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 480px) {
    .back-btn {
      min-width: 100px;
      padding: 10px 20px;
    }
  }
  
  .basic-info {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      margin: 2rem 0;
      padding: 2rem;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }
  .basic-info div {
      padding: 1rem;
  }
  
  /* 内容区块样式 */
  .section {
      margin: calc(2rem * 2) 0;
      padding: 2rem;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
      text-align: left;
  }
  
  /* 列表样式 */
  ul, ol {
      margin-left: 1.5rem;
      /* margin-bottom: 2rem; */
  }
  
  li {
      margin-bottom: 0.5rem;
      color: #707070;
  }
  
  /* 情绪象限样式 */
  .quadrant-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
      margin: 2rem 0;
  }
  
  .quadrant {
      padding: 2rem;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e6e6e6;
  }
  
  /* 建议卡片样式 */
  .suggestion-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin: 2rem 0;
  }
  
  .suggestion-item {
      padding: 2rem;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e6e6e6;
  }
  
  /* 参考文献样式 */
  .reference {
      padding: 1rem 0;
      border-bottom: 1px solid #e6e6e6;
  }
  
  .reference:last-child {
      border-bottom: none;
  }
  
  /* 脚注样式 */
  .footer {
      margin-top: calc(2rem * 3);
      padding-top: 2rem;
      border-top: 1px solid #e6e6e6;
      text-align: center;
      color: #707070;
      font-size: 0.9rem;
      text-align: left;
  }
  
  .signature {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #e6e6e6;
  }
  
  /* 应急联系方式样式 */
  .emergency-contacts {
      background-color: #fff8f8;
      padding: 2rem;
      border-radius: 4px;
      margin: 2rem 0;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
  
      .container {
          padding: 0 1rem;
      }
  
      .basic-info,
      .quadrant-grid {
          grid-template-columns: 1fr;
      }
  
      h1 {
          font-size: 2rem;
      }
  
      h2 {
          font-size: 1.6rem;
      }
  
      h3 {
          font-size: 1.2rem;
      }
  }

.au-grid {
  display: grid;
  grid-template-columns: minmax(100px, auto) 1fr 80px;
  gap: 10px 15px;
  align-items: center;
}

.expression .au-grid {
  grid-template-columns: minmax(80px, auto) 1fr 80px;
}

.au-item {
  display: contents;
}

.au-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

.progress-bar {
  height: 20px;
  background-color: #e0e0e0;
  position: relative;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.5s ease-in-out;
  position: absolute;
  left: 0;
  top: 0;
}

.percentage {
  text-align: right;
  font-size: 14px;
  padding-left: 5px;
}

@media print {
  .button-container {
    display: none;
  }
  .speech-analysis {
    display: none;
  }
  .speech-sugguestion {
    display: none;
  }
  .speech-imporvement {
    display: none;
  }
  .speech-future-plan {
    display: none;
  }
  .speech-tips {
    display: none;
  }
  .emergency-contacts {
    display: none;
  }
  .speech-reference {
    display: none;
  }
}
</style>