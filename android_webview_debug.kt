// 改进后的Android WebView拦截代码，包含详细的调试日志

inner class HrvWebViewClient : CustomWebViewClient(){
    override fun onPageFinished(view: WebView?, url: String?) {
        super.onPageFinished(view, url)
        Log.d("HrvWebView", "Page finished loading: $url")
        hrvActionListener?.onPageFinished()
    }

    override fun shouldInterceptRequest(
        view: WebView?,
        request: WebResourceRequest?
    ): WebResourceResponse? {
        val url = request?.url.toString()
        Log.d("HrvWebView", "Intercepting request: $url")

        // 拦截对opencv.js和face_landmarker.task的访问
        if (shouldInterceptConfigResource(url)) {
            Log.d("HrvWebView", "Attempting to intercept config resource: $url")
            try {
                val fileName: String = getFileNameFromUrl(url)
                Log.d("HrvWebView", "Extracted filename: $fileName")
                
                // 检查文件是否存在
                val assetPath = "configs/$fileName"
                Log.d("HrvWebView", "Looking for asset: $assetPath")
                
                val inputStream = context.assets.open(assetPath)
                val mimeType = getMimeType(fileName)
                Log.d("HrvWebView", "Successfully loaded local file: $fileName with MIME type: $mimeType")
                
                val response = WebResourceResponse(mimeType, "UTF-8", inputStream)
                Log.d("HrvWebView", "Returning intercepted response for: $fileName")
                return response
            } catch (e: IOException) {
                Log.e("HrvWebView", "Failed to load local file from assets: ${e.message}")
                Log.e("HrvWebView", "Stack trace:", e)
                // 返回null让WebView尝试从网络加载
                return null
            }
        }

        // 不需要拦截的资源返回null，让WebView自己处理
        return null
    }
}

private fun getMimeType(fileName: String): String {
    Log.d("HrvWebView", "Getting MIME type for: $fileName")
    val mimeType = when {
        fileName.endsWith(".js") -> "application/javascript"
        fileName.endsWith(".css") -> "text/css"
        fileName.endsWith(".png") -> "image/png"
        fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") -> "image/jpeg"
        fileName.endsWith(".woff2") -> "font/woff2"
        fileName.endsWith(".task") -> "application/octet-stream"
        else -> "text/plain"
    }
    Log.d("HrvWebView", "MIME type for $fileName: $mimeType")
    return mimeType
}

private fun shouldInterceptLocalResource(url: String): Boolean {
    // 定义需要本地加载的资源类型和路径
    val localPaths: List<String> = mutableListOf(
        "/opencv",
        "/mediapipe"
    )

    for (path in localPaths) {
        if (url.contains(path)) {
            Log.d("HrvWebView", "Should intercept local resource: $url (matches $path)")
            return true
        }
    }
    return false
}

private fun shouldInterceptConfigResource(url: String): Boolean {
    // 定义需要从configs目录加载的资源
    val configFiles: List<String> = mutableListOf(
        "opencv.js",
        "face_landmarker.task"
    )

    for (fileName in configFiles) {
        if (url.contains(fileName)) {
            Log.d("HrvWebView", "Should intercept config resource: $url (matches $fileName)")
            return true
        }
    }
    Log.d("HrvWebView", "Should NOT intercept: $url")
    return false
}

private fun getFileNameFromUrl(url: String): String {
    // 从URL中提取文件名
    val fileName = when {
        url.contains("opencv.js") -> "opencv.js"
        url.contains("face_landmarker.task") -> "face_landmarker.task"
        else -> {
            // 作为备用方案，从URL路径中提取最后一部分
            val uri = Uri.parse(url)
            uri.lastPathSegment ?: "unknown"
        }
    }
    Log.d("HrvWebView", "Extracted filename from URL '$url': $fileName")
    return fileName
}

// 在WebView初始化时添加调试信息
private fun setupWebView() {
    // ... 其他WebView配置 ...
    
    // 启用调试模式
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
        WebView.setWebContentsDebuggingEnabled(true)
    }
    
    // 设置WebViewClient
    webView.webViewClient = HrvWebViewClient()
    
    // 添加JavaScript接口用于调试
    webView.addJavascriptInterface(object {
        @JavascriptInterface
        fun logFromJS(message: String) {
            Log.d("HrvWebView-JS", message)
        }
    }, "AndroidDebug")
    
    Log.d("HrvWebView", "WebView setup completed with debugging enabled")
}
