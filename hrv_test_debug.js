// HRV测试调试工具 - 调用Android WebView JavascriptInterface
// 直接在浏览器控制台中运行，调用Android原生方法

class HRVTestDebugger {
    constructor() {
        this.TAG = "HRVTestDebugger";
        this.startTime = Date.now();
        this.init();
    }

    // 初始化调试器
    init() {
        console.log(`🚀 ${this.TAG} 初始化完成`);
        console.log(`📍 当前URL: ${window.location.href}`);
        console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);

        // 检查Android WebView接口是否可用
        this.checkAndroidInterface();

        console.log("✅ 调试器已准备就绪");
    }

    // 检查Android接口是否可用
    checkAndroidInterface() {
        console.log("🔍 检查可用的全局对象...");

        // 检查常见的Android接口名称
        const possibleNames = ['Android', 'AndroidInterface', 'NativeInterface', 'WebViewInterface'];
        let foundInterface = null;

        for (const name of possibleNames) {
            if (typeof window[name] !== 'undefined') {
                foundInterface = name;
                console.log(`✅ 找到Android接口: ${name}`);
                console.log(`📱 ${name}可用方法:`, Object.getOwnPropertyNames(window[name]));
                break;
            }
        }

        if (!foundInterface) {
            console.warn("⚠️ 未检测到Android WebView接口");
            console.log("� 当前window对象的所有属性:");
            console.log(Object.getOwnPropertyNames(window).filter(name =>
                !name.startsWith('webkit') &&
                !name.startsWith('chrome') &&
                typeof window[name] === 'object' &&
                window[name] !== null
            ));
            console.log("💡 请检查Android代码中的addJavascriptInterface调用");
        }

        return foundInterface;
    }

    // 模拟Logger.d方法
    logD(tag, msg) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] 🔍 ${tag}: ${msg}`);
    }

    // 模拟Logger.e方法
    logE(tag, msg) {
        const timestamp = new Date().toLocaleTimeString();
        console.error(`[${timestamp}] ❌ ${tag}: ${msg}`);
    }

    // 模拟MMKV存储
    saveToStorage(key, value) {
        try {
            localStorage.setItem(key, value);
            console.log(`💾 数据已保存到localStorage: ${key}`);
        } catch (e) {
            console.error(`❌ 保存失败: ${e.message}`);
        }
    }

    // 调用Android的saveAssessmentResult方法
    callSaveAssessmentResult(resultJson) {
        try {
            console.log("� 准备调用Android方法: saveAssessmentResult");
            console.log(`📊 传入数据长度: ${resultJson.length}`);
            console.log(`📊 传入数据预览: ${resultJson.substring(0, 100)}${resultJson.length > 100 ? "..." : ""}`);

            if (typeof window.Android !== 'undefined' && typeof window.Android.saveAssessmentResult === 'function') {
                console.log("✅ 调用Android.saveAssessmentResult...");
                window.Android.saveAssessmentResult(resultJson);
                console.log("✅ Android方法调用完成");
            } else {
                console.error("❌ Android.saveAssessmentResult 方法不可用");
                console.log("💡 请确保在Android WebView环境中运行");
            }
        } catch (e) {
            console.error(`❌ 调用saveAssessmentResult失败: ${e.message}`);
        }
    }

    // 调用Android的stopGazeTracking方法
    callStopGazeTracking() {
        try {
            console.log("� 准备调用Android方法: stopGazeTracking");

            if (typeof window.Android !== 'undefined' && typeof window.Android.stopGazeTracking === 'function') {
                console.log("✅ 调用Android.stopGazeTracking...");
                window.Android.stopGazeTracking();
                console.log("✅ Android方法调用完成");
            } else {
                console.error("❌ Android.stopGazeTracking 方法不可用");
                console.log("� 请确保在Android WebView环境中运行");
            }
        } catch (e) {
            console.error(`❌ 调用stopGazeTracking失败: ${e.message}`);
        }
    }

    // 测试方法 - 生成模拟数据并测试保存
    testSaveAssessmentResult() {
        const mockData = {
            userId: "test_user_001",
            testType: "HRV_ASSESSMENT",
            startTime: Date.now() - 300000, // 5分钟前开始
            endTime: Date.now(),
            results: {
                heartRate: 72,
                hrvScore: 85,
                stressLevel: "low",
                recommendations: ["保持当前状态", "适量运动"]
            },
            metadata: {
                deviceType: "web",
                browserInfo: navigator.userAgent,
                screenResolution: `${screen.width}x${screen.height}`
            }
        };

        console.log("🧪 开始测试 saveAssessmentResult...");
        this.callSaveAssessmentResult(JSON.stringify(mockData));
    }

    // 测试方法 - 测试停止眼动追踪
    testStopGazeTracking() {
        console.log("🧪 开始测试 stopGazeTracking...");
        this.callStopGazeTracking();
    }

    // 查看保存的数据
    viewSavedData() {
        const savedData = localStorage.getItem("HRV_ASSESSMENT_RESULT");
        if (savedData) {
            console.log("📋 已保存的评估结果:");
            console.log(JSON.parse(savedData));
        } else {
            console.log("📋 暂无保存的评估结果");
        }
    }

    // 清除保存的数据
    clearSavedData() {
        localStorage.removeItem("HRV_ASSESSMENT_RESULT");
        console.log("🗑️ 已清除保存的评估结果");
    }

    // 显示帮助信息
    showHelp() {
        console.log(`
🔧 HRV测试调试器使用说明:

Android方法调用:
• hrvDebugger.callSaveAssessmentResult(jsonString) - 调用Android保存评估结果
• hrvDebugger.callStopGazeTracking() - 调用Android停止眼动追踪

直接调用Android接口:
• Android.saveAssessmentResult(jsonString) - 直接调用Android方法
• Android.stopGazeTracking() - 直接调用Android方法

测试方法:
• hrvDebugger.testSaveAssessmentResult() - 测试保存功能
• hrvDebugger.testStopGazeTracking() - 测试停止追踪功能

数据管理:
• hrvDebugger.viewSavedData() - 查看保存的数据
• hrvDebugger.clearSavedData() - 清除保存的数据

示例用法:
hrvDebugger.callSaveAssessmentResult('{"test": "data"}');
Android.saveAssessmentResult('{"test": "data"}');
        `);
    }
}

// 自动初始化调试器
console.log("🎯 正在初始化HRV测试调试器...");
const hrvDebugger = new HRVTestDebugger();

// 显示帮助信息
hrvDebugger.showHelp();

// 导出到全局作用域
window.hrvDebugger = hrvDebugger;

console.log("✨ 调试器已就绪！输入 hrvDebugger.showHelp() 查看使用说明");
