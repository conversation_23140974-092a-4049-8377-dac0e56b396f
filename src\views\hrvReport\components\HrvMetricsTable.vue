<template>
  <div>
    <table class="hrv-table">
      <thead>
      <!-- 第一行表格标题，背景颜色为深绿色，左对齐 -->
      <tr>
        <th colspan="4" class="header-title">{{ $t('hrv_param_key')}}</th>
      </tr>
      <!-- 第二行表头：时域分析参数和频域分析参数 -->
      <tr>
        <th colspan="2" class="sub-header" style="width: 50%">{{ $t('time_domain_param_key')}}</th>
        <th colspan="2" class="sub-header" style="width: 50%">{{ $t('frequency_domain_param_key')}}</th>
      </tr>
      <!-- 第三行表头：指标、数值和参考范围 -->
      <tr class="bold-header">
        <th>{{$t('metric_key')}}</th>
        <th>{{$t('figure_key')}}</th>
        <!-- <th>参考范围</th> -->
        <th>{{$t('metric_key')}}</th>
        <th>{{$t('figure_key')}}</th>
        <!-- <th>参考范围</th> -->
      </tr>
      </thead>
      <tbody>
      <tr v-for="index in maxLength" :key="index - 1">
        <!-- 时域参数 -->
        <td>{{ timeDomainKeys[index - 1] || '' }}</td>
        <td>{{ timeDomainMetrics[timeDomainKeys[index - 1]]?.value || '' }}</td>
        <!-- <td>{{ timeDomainMetrics[timeDomainKeys[index - 1]]?.range || '' }}</td> -->

        <!-- 频域参数 -->
        <td>{{ frequencyDomainKeys[index - 1] || '' }}</td>
        <td>{{ frequencyDomainMetrics[frequencyDomainKeys[index - 1]]?.value || '' }}</td>
        <!-- <td>{{ frequencyDomainMetrics[frequencyDomainKeys[index - 1]]?.range || '' }}</td> -->
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';

const timeDomainMetrics = ref({})
const frequencyDomainMetrics = ref({})
const maxLength = ref([])
const timeDomainKeys = ref([])
const frequencyDomainKeys = ref([])

// 定义 props 来接收传入的时域和频域数据
const props = defineProps({
  // timeDomainMetrics: Object,
  // frequencyDomainMetrics: Object,
  hrvInfo: Object,
});

const formatNumber = (num) => {
  return Number(num).toFixed(2);
};

const formathrvInfo = (hrvInfo) => {
console.log("🚀 ~ formathrvInfo ~ hrvInfo:", hrvInfo)

  const timeDomainMetrics_ = {
    'MeanNN(ms)': { value: formatNumber(hrvInfo.HRV_MeanNN), range: '600-1200' },
    'SDNN(ms)': { value: formatNumber(hrvInfo.HRV_SDNN), range: '20-220' },
    'RMSSD(ms)': { value: formatNumber(hrvInfo.HRV_RMSSD), range: '10-150' },
    'SDSD(ms)': { value: formatNumber(hrvInfo.HRV_SDSD), range: '15-120' },
    'pNN50': { value: formatNumber(hrvInfo.HRV_pNN50), range: '3-62' }
  };

  const frequencyDomainMetrics_ = {
    'TP': { value: formatNumber(hrvInfo.HRV_TP), range: '3-20' },
    'VLF': { value: formatNumber(hrvInfo.HRV_VLF), range: '50-100' },
    'LF': { value: formatNumber(hrvInfo.HRV_LF), range: '50-100' },
    'HF': { value: formatNumber(hrvInfo.HRV_HF), range: '20-50' },
    'LF/HF': { value: formatNumber(hrvInfo.HRV_LFHF), range: '20-50' }
  };

  const timeDomainKeys_ = Object.keys(timeDomainMetrics_);
  const frequencyDomainKeys_ = Object.keys(frequencyDomainMetrics_);

  // 计算最大行数（确保两部分的行数对齐）
  maxLength.value = Math.max(timeDomainKeys_.length, frequencyDomainKeys_.length);

  timeDomainMetrics.value = timeDomainMetrics_
  frequencyDomainMetrics.value = frequencyDomainMetrics_
  timeDomainKeys.value = timeDomainKeys_
  frequencyDomainKeys.value = frequencyDomainKeys_

}

onMounted(() => {

});

watch(() => props.hrvInfo, () => {
  formathrvInfo(props.hrvInfo)
},{
  deep: true,
  immediate: true,
});

</script>

<style scoped>
.hrv-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.hrv-table th,
.hrv-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

/* 确保表头左对齐，并设置深绿色背景 */
.header-title {
  text-align: left !important; /* 强制左对齐 */
  background-color: #17d7e2; /* 深绿色背景 */
  color: white;
  font-weight: bold;
}

/* 确保时域和频域表头加粗 */
.sub-header {
  font-weight: bold;
}

/* 确保"指标"、"数值"等标题加粗 */
.bold-header th {
  font-weight: bold;
}
</style>
