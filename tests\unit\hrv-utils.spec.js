import { calculateHRVMetrics } from '@/utils/hrv-utils';

describe('HRV Utils Tests', () => {
  test('calculateHRVMetrics computes correct HRV metrics for sample data', () => {
    const rrIntervals = [
      860, 840, 870, 855, 850, 865, 845, 860, 875, 835,
      855, 870, 840, 850, 860, 845, 855, 865, 850, 860,
    ];

    const hrvMetrics = calculateHRVMetrics(rrIntervals);

    // 预期值
    const expectedTimeDomainMetrics = {
      SDNN: 12.58,
      RMSSD: 15.81,
      NN50: 8,
      pNN50: 42.11,
      averageHeartRate: 69.97,
    };

    // 断言时域指标
    expect(hrvMetrics.timeDomain.SDNN).toBeCloseTo(expectedTimeDomainMetrics.SDNN, 2);
    expect(hrvMetrics.timeDomain.RMSSD).toBeCloseTo(expectedTimeDomainMetrics.RMSSD, 2);
    expect(hrvMetrics.timeDomain.NN50).toBe(expectedTimeDomainMetrics.NN50);
    expect(hrvMetrics.timeDomain.pNN50).toBeCloseTo(expectedTimeDomainMetrics.pNN50, 2);
    expect(hrvMetrics.timeDomain.averageHeartRate).toBeCloseTo(expectedTimeDomainMetrics.averageHeartRate, 2);

    // 如果需要，可以添加频域和非线性指标的断言
  });
});