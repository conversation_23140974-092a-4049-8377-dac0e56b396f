<template>
  <div v-show="metrics.showMetrics" ref="metricsSection" class="metrics-section" @mousedown="startDrag">
    <div class="metrics">
      <p>心率: {{ metrics.heartRate }} bpm</p>
      <p>MeanRR: {{ metrics.meanRR }} ms</p>
      <p>SDNN: {{ metrics.sdnn }} ms</p>
      <p>RMSSD: {{ metrics.rmssd }} ms</p>
      <p>pNN50：{{ metrics.pnn50 }}%</p>
      <p>LF：{{ metrics.lf }} ms&sup2</p>
      <p>HF：{{ metrics.hf }} ms&sup2</p>
      <p>LF/HF：{{ metrics.lf_hf_ratio }}</p>
      <p>FPS：{{ metrics.fps }}</p>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, nextTick} from 'vue';

const props = defineProps({
  metrics: Object,  // 使用一个数据对象传递所有生理指标
});

const metricsSection = ref(null);
let isDragging = false;
let startX, startY, initialLeft, initialTop;

const startDrag = (event) => {
  isDragging = true;
  startX = event.clientX;
  startY = event.clientY;
  initialLeft = metricsSection.value.offsetLeft;
  initialTop = metricsSection.value.offsetTop;
  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', stopDrag);
};

const drag = (event) => {
  if (!isDragging) return;

  const deltaX = event.clientX - startX;
  const deltaY = event.clientY - startY;

  let newLeft = initialLeft + deltaX;
  let newTop = initialTop + deltaY;

  const parentWidth = metricsSection.value.parentElement.clientWidth;
  const parentHeight = metricsSection.value.parentElement.clientHeight;

  const maxLeft = parentWidth - metricsSection.value.clientWidth;
  const maxTop = parentHeight - metricsSection.value.clientHeight;

  newLeft = Math.min(Math.max(0, newLeft), maxLeft);
  newTop = Math.min(Math.max(0, newTop), maxTop);

  metricsSection.value.style.left = `${newLeft}px`;
  metricsSection.value.style.top = `${newTop}px`;
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', drag);
  document.removeEventListener('mouseup', stopDrag);
};

</script>

<style scoped>
.metrics-section {
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 10px;
  top: 10px;
  width: 280px;
  color: #f5f5f5;
  padding: 10px;
  background: linear-gradient(to right, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.1));
  border-radius: 15px;
}

.metrics p {
  margin: 4px 0;
}

.additional-metrics {
  margin-top: 5px;
}
</style>
