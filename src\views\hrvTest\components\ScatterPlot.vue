<template>
	<div
		ref="chartContainer"
		style="width: 100%; height: 100%; aspect-ratio: 1/1"
	></div>
</template>

<script setup>
import * as echarts from "echarts"
import { ref, onMounted, onUnmounted, defineProps, watch } from "vue"
const props = defineProps({
	chartData: {
		type: Object,
		required: true,
	},
})
const chartContainer = ref(null)
let myChart = null

function setChartOption() {
	if (myChart && props.chartData) {
		const option = {
			grid: {
				left: "5%",
				right: "5%",
				top: "5%",
				bottom: "5%",
				containLabel: true,
			},
			xAxis: [
				{
					min: 1,
					max: 7,
					nameTextStyle: { fontSize: 16 },
					axisLine: { show: true },
					axisTick: { show: true, inside: true },
					axisLabel: { show: true },
					splitLine: { show: false },
				},
				{
					position: "top",
					min: 1,
					max: 7,
					axisLine: { show: true },
					axisTick: { show: false },
					axisLabel: { show: false },
					splitLine: { show: false },
				},
			],
			yAxis: [
				{
					min: 1,
					max: 7,
					nameTextStyle: { fontSize: 16 },
					axisLine: { show: true },
					axisTick: { show: true, inside: true },
					axisLabel: { show: true },
					splitLine: { show: false },
				},
				{
					position: "right",
					min: 1,
					max: 7,
					axisLine: { show: true },
					axisTick: { show: false },
					axisLabel: { show: false },
					splitLine: { show: false },
				},
			],
			series: [
				{
					symbolSize: 8,
					data: [[props.chartData.valence, props.chartData.arousal]],
					type: "scatter",
					itemStyle: {
						color: "transparent",
						borderColor: "rgba(235, 78, 137, 1)",
						borderWidth: 2,
					},
				},
			],
			labelLine: {
				show: false,
			},
		}
		myChart.setOption(option, true)
	}
}

onMounted(() => {
	if (chartContainer.value) {
		myChart = echarts.init(chartContainer.value)
		setChartOption()
		window.addEventListener("resize", myChart.resize)
	}
})

watch(
	() => props.chartData,
	() => {
		setChartOption()
	},
	{ deep: true }
)

onUnmounted(() => {
	if (myChart) {
		window.removeEventListener("resize", myChart.resize)
		myChart.dispose()
	}
})
</script>

<style scoped>
/* 在这里添加组件特有的样式 */
</style>
