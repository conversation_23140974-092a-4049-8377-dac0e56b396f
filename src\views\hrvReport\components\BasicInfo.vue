<template>
	<div class="basic-info">
		<div class="basic-grid">
			<div class="basic-grid-item">
				<strong>{{ $t("username_key") }}: </strong
				><span>{{ basicInfo.name }}</span>
			</div>
			<div class="basic-grid-item">
				<strong>{{ $t("gender_key") }}: </strong
				><span>{{ basicInfo.gender }}</span>
			</div>
			<div class="basic-grid-item">
				<strong>{{ $t("age_key") }}: </strong
				><span>{{ basicInfo.age }}</span>
			</div>
			<div class="basic-grid-item">
				<strong>{{ $t("idnum_key") }}: </strong
				><span>{{ basicInfo.bizNo }}</span>
			</div>
			<div class="basic-grid-item">
				<strong>{{ $t("check_time_key") }}: </strong
				><span
					>{{ basicInfo.assessmentTime }}

					<!-- ({{basicInfo.timezone}}) -->
				</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from "vue"
import { useI18n } from "vue-i18n"

const { t } = useI18n()
const basicInfo = ref({})

// Helper function to chunk the object into groups of 3 key-value pairs
const chunkObject = (obj, size) => {
	const entries = Object.entries(obj) // Convert the object to an array of [key, value] pairs
	return entries.reduce((result, entry, index) => {
		const chunkIndex = Math.floor(index / size) // Determine which chunk to put the entry in
		if (!result[chunkIndex]) result[chunkIndex] = {} // Create a new chunk if necessary
		result[chunkIndex][entry[0]] = entry[1] // Add the key-value pair to the chunk
		return result
	}, [])
}

// Define the prop to receive the entire BasicInfo object
const props = defineProps({
	info: {
		type: Object,
		required: true,
	},
})

// Chunk the info object into groups of 3 key-value pairs
// const chunkedInfo = chunkObject(props.info, 3);

const formatBasicInfo = () => {
	const info = props.info || {}
	console.log("🚀 ~ formatBasicInfo ~ props.info:", props.info)
	const gender_str = info.gender === "male" ? t("male_key") : t("female_key")
	const username_str = info.name ? info.name : ""
	const age_str = info.age ? info.age : ""

	basicInfo.value.name = username_str
	basicInfo.value.gender = gender_str
	basicInfo.value.age = age_str
	basicInfo.value.bizNo = info.bizNo
	basicInfo.value.assessmentTime = info.assessmentTime
	basicInfo.value.timezone = info.timezone
}

onMounted(() => {})

watch(
	() => props.info,
	() => {
		formatBasicInfo()
	}
)
</script>

<style scoped>
.basic-info {
	margin-bottom: 20px;
	padding: 0 8px;
	gap: 15px 0;
}

.basic-info .basic-grid {
	display: flex;
	flex-wrap: wrap;
}

.basic-info .basic-grid-item {
	border-bottom: 1px solid #ddd;
	padding: 0.5rem;
	min-width: 30%;
}

.basic-info .basic-grid-item span {
	padding: 0 0.5rem;
}

.basic-info table {
	width: 100%;
	border-collapse: collapse;
}

.basic-info td {
	padding: 8px;
	border-bottom: 1px solid #ddd;
}

.basic-info td:first-child {
	font-weight: bold;
}

.basic-info h3 {
	margin-bottom: 10px;
}
</style>
