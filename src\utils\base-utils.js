import { saveAs } from 'file-saver';

export function saveToCSV(headers, data, fileNamePrefix) {
  // 生成 CSV 内容
  let csvContent = headers.join(",") + "\n";
  data.forEach(row => {
    csvContent += row.join(",") + "\n";
  });

  // 创建 CSV 文件
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });

  // 获取当前时间作为文件名
  const now = new Date();
  const fileName = `${fileNamePrefix}-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}.csv`;

  // 使用 FileSaver.js 保存文件
  saveAs(blob, fileName);
}
