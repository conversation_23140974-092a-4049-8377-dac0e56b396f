<template>
  <div class="emotion-report">
    <h1>{{$t('emotion_report_title_key')}}</h1>
    <div v-if="hasResults">

      <div class="basic-info">
        <div>
          <span>{{ $t('evaluation_date_key') }}{{$t('colon_key')}}</span>
          <span class="emphasis">{{ screen_time }}</span>
        </div>
        <div>
          <span>{{$t('evaluation_method_key')}}{{$t('colon_key')}}</span>
          <span class="emphasis">{{$t('emotion_sys_ai_key')}}</span>
        </div>
        <div>
          <span>{{$t('report_number_key')}}{{$t('colon_key')}}</span>
          <span class="emphasis">{{ screen_idx }}</span>
        </div>
      </div>

      <div class="section">
        <h2>{{$t('emotion_report_section1_key')}}</h2>
        <p>{{$t('dear_key')}} {{ screen_name }}：</p>
        <p>{{$t('emotion_report_section1_desc_key')}}</p>

        <div v-for="(result, index) in analysisResults" :key="index" class="face-emotion-container">
          <h2>Face {{ index + 1 }}</h2>
          <FaceEmotion :result="result" :index="index + 1" />
        </div>
      </div>

      <div class="section emotion-analysis">
        <h2>{{$t('emotion_report_section2_key')}}</h2>
        <h3>{{$t('emotion_report_section2_h3_key')}}</h3>
        <p>{{$t('emotion_report_section2_h3_desc1_key')}} {{ arousal_str }} {{$t('emotion_report_section2_h3_desc2_key')}}</p>
        <ul>
          <li>{{$t('emotion_report_section2_h3_li1_key')}}</li>
          <li>{{$t('emotion_report_section2_h3_li2_key')}}</li>
        </ul>
        <h3>{{$t('emotion_report_section2_h3_2_key')}}</h3>
        <p>{{$t('emotion_report_section2_h3_2_desc1_key')}} {{ valence_str }} {{$t('emotion_report_section2_h3_2_desc2_key')}}</p>
        <ul>
          <li>{{$t('emotion_report_section2_h3_2_li1_key')}}</li>
          <li>{{$t('emotion_report_section2_h3_2_li2_key')}}</li>
          <!-- <li>中性：如平静的湖面</li> -->
        </ul>
        <h3>{{$t('emotion_report_section2_h3_3_key')}}</h3>
        <div class="quadrant-grid">
          <div class="quadrant">
              <h4>{{$t('emotion_report_section2_h3_3_xy1_key')}}</h4>
              <p>{{$t('emotion_report_section2_h3_3_xy1_desc1_key')}}</p>
              <p>{{$t('emotion_report_section2_h3_3_xy1_desc2_key')}}</p>
              <ul>
                  <li>{{$t('emotion_report_section2_h3_3_xy1_desc2_li1_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy1_desc2_li2_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy1_desc2_li3_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy1_desc2_li4_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy1_desc2_li5_key')}}</li>
              </ul>
          </div>

          <div class="quadrant">
              <h4>{{$t('emotion_report_section2_h3_3_xy2_key')}}</h4>
              <p>{{$t('emotion_report_section2_h3_3_xy2_desc1_key')}}</p>
              <p>{{$t('emotion_report_section2_h3_3_xy1_desc2_key')}}</p>
              <ul>
                  <li>{{$t('emotion_report_section2_h3_3_xy2_desc2_li1_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy2_desc2_li2_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy2_desc2_li3_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy2_desc2_li4_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy2_desc2_li5_key')}}</li>
              </ul>
          </div>

          <div class="quadrant">
              <h4>{{$t('emotion_report_section2_h3_3_xy3_key')}}</h4>
              <p>{{$t('emotion_report_section2_h3_3_xy3_desc1_key')}}</p>
              <p>{{$t('emotion_report_section2_h3_3_xy1_desc2_key')}}</p>
              <ul>
                  <li>{{$t('emotion_report_section2_h3_3_xy3_desc2_li1_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy3_desc2_li2_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy3_desc2_li3_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy3_desc2_li4_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy3_desc2_li5_key')}}</li>
              </ul>
          </div>
          <div class="quadrant">
              <h4>{{$t('emotion_report_section2_h3_3_xy4_key')}}</h4>
              <p>{{$t('emotion_report_section2_h3_3_xy4_desc1_key')}}</p>
              <p>{{$t('emotion_report_section2_h3_3_xy1_desc2_key')}}</p>
              <ul>
                  <li>{{$t('emotion_report_section2_h3_3_xy4_desc2_li1_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy4_desc2_li2_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy4_desc2_li3_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy4_desc2_li4_key')}}</li>
                  <li>{{$t('emotion_report_section2_h3_3_xy4_desc2_li5_key')}}</li>
              </ul>
          </div>
        </div>
      </div>

      <div class="section emotion-sugguestion">
        <h2>{{$t('emotion_report_section3_key')}}</h2>
        <p>{{$t('emotion_report_section3_key_desc1')}}</p>
        <h3>{{$t('emotion_report_section3_key_desc2_1')}}</h3>
        <div class="suggestion-list">
            <div class="suggestion-item">
                <h4>{{$t('emotion_report_section3_key_desc2_1_a')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section3_key_desc2_1_a_li1')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_a_li2')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_a_li3')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_a_li4')}}</li>
                </ul>

                <h4>{{t('emotion_report_section3_key_desc2_1_b')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section3_key_desc2_1_b_li1')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_b_li2')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_b_li3')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_b_li4')}}</li>
                </ul>
            </div>

            <div class="suggestion-item">
                <h4>{{$t('emotion_report_section3_key_desc2_1_c')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section3_key_desc2_1_c_li1')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_c_li2')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_c_li3')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_c_li4')}}</li>
                </ul>

                <h4>{{$t('emotion_report_section3_key_desc2_1_d')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section3_key_desc2_1_d_li1')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_d_li2')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_d_li3')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_1_d_li4')}}</li>
                </ul>
            </div>
        </div>

        <h3>{{$t('emotion_report_section3_key_desc2_2')}}</h3>
        <p>{{$t('emotion_report_section3_key_desc2')}}</p>
        <div class="suggestion-list">
            <div class="suggestion-item">
             
              <h4>{{$t('emotion_report_section3_key_desc2_2_a')}}</h4>
              <ul>
                  <li>{{$t('emotion_report_section3_key_desc2_2_a_li1')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_a_li2')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_a_li3')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_a_li4')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_a_li5')}}</li>
              </ul>
              <h4>{{$t('emotion_report_section3_key_desc2_2_b')}}</h4>
              <ul>
                  <li>{{$t('emotion_report_section3_key_desc2_2_b_li1')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_b_li2')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_b_li3')}}</li>
                  <li>{{$t('emotion_report_section3_key_desc2_2_b_li4')}}</li>
              </ul>
            </div>
            <div class="suggestion-item">
                <h4>{{$t('emotion_report_section3_key_desc2_2_c')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section3_key_desc2_2_c_li1')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_2_c_li2')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_2_c_li3')}}</li>
                    <li>{{$t('emotion_report_section3_key_desc2_2_c_li4')}}</li>
                </ul>
            </div>
        </div>
      </div>

      <div class="section emotion-imporvement">
        <h2>{{$t('emotion_report_section4_key')}}</h2>

        <div class="suggestion-list">
            <div class="suggestion-item">
                <h3>{{$t('emotion_report_section4_key_1')}}</h3>
                <ul>
                    <li>{{$t('emotion_report_section4_key_1_li1')}}</li>
                    <li>{{$t('emotion_report_section4_key_1_li2')}}</li>
                    <li>{{$t('emotion_report_section4_key_1_li3')}}</li>
                    <li>{{$t('emotion_report_section4_key_1_li4')}}</li>
                    <li>{{$t('emotion_report_section4_key_1_li5')}}</li>
                </ul>
            </div>

            <div class="suggestion-item">
                <h3>{{$t('emotion_report_section4_key_2')}}</h3>
                <ul>
                    <li>{{$t('emotion_report_section4_key_2_li1')}}</li>
                    <li>{{$t('emotion_report_section4_key_2_li2')}}</li>
                    <li>{{$t('emotion_report_section4_key_2_li3')}}</li>
                    <li>{{$t('emotion_report_section4_key_2_li4')}}</li>
                    <li>{{$t('emotion_report_section4_key_2_li5')}}</li>
                </ul>
            </div>
        </div>
      </div>

      <div class="section emotion-future-plan">
        <h2>{{$t('emotion_report_section5_key')}}</h2>

        <div class="suggestion-list">
            <div class="suggestion-item">
                <h3>{{$t('emotion_report_section5_key_1')}}</h3>
                
                <h4>{{$t('emotion_report_section5_key_1_a')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_1_a_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_a_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_a_li3')}}</li>
                </ul>

                <h4>{{$t('emotion_report_section5_key_1_b')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_1_b_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_b_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_b_li3')}}</li>
                </ul>

                <h4>{{$t('emotion_report_section5_key_1_c')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_1_c_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_c_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_1_c_li3')}}</li>
                </ul>
            </div>

            <div class="suggestion-item">
                <h3>{{$t('emotion_report_section5_key_2')}}</h3>
                
                <h4>{{$t('emotion_report_section5_key_2_a')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_2_a_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_a_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_a_li3')}}</li>
                </ul>

                <h4>{{$t('emotion_report_section5_key_2_b')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_2_b_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_b_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_b_li3')}}</li>
                </ul>

                <h4>{{$t('emotion_report_section5_key_2_c')}}</h4>
                <ul>
                    <li>{{$t('emotion_report_section5_key_2_c_li1')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_c_li2')}}</li>
                    <li>{{$t('emotion_report_section5_key_2_c_li3')}}</li>
                </ul>
            </div>
        </div>
      </div>

      <div class="section emotion-tips">
        <h2>{{$t('emotion_report_section6_key')}}</h2>
        <ul>
            <li>{{$t('emotion_report_section6_key_li1')}}</li>
            <li>{{$t('emotion_report_section6_key_li2')}}</li>
            <li>{{$t('emotion_report_section6_key_li3')}}</li>
        </ul>
      </div>

      <div class="section emergency-contacts">
          <h2>{{$t('emotion_report_section7_key')}}</h2>
          <ul>
              <li>{{$t('emotion_report_section7_key_li1')}}</li>
              <li>{{$t('emotion_report_section7_key_li2')}}</li>
              <li>{{$t('emotion_report_section7_key_li3')}}</li>
          </ul>
      </div>

      <div class="section emotion-reference">
        <h2>{{$t('emotion_report_section8_key')}}</h2>
        <div class="reference">
            <p>1. World Health Organization. (2020). "Mental health and psychosocial considerations during the COVID-19 outbreak." WHO Reference Number: WHO/2019-nCoV/MentalHealth/2020.1</p>
            <p class="reference-note">- {{$t('emotion_report_section8_key_p1')}}</p>
        </div>
        
        <div class="reference">
            <p>2. American Psychiatric Association. (2022). "Diagnostic and Statistical Manual of Mental Disorders (DSM-5-TR)."</p>
            <p class="reference-note">- {{$t('emotion_report_section8_key_p2')}}</p>
        </div>
        
        <div class="reference">
            <p>3. National Institute of Mental Health. (2021). "Depression: What You Need to Know."</p>
            <p class="reference-note">- {{$t('emotion_report_section8_key_p3')}}</p>
        </div>
        
        <div class="reference">
            <p>4. Beck, A. T., &amp; Alford, B. A. (2009). "Depression: Causes and Treatment." University of Pennsylvania Press.</p>
            <p class="reference-note">- {{$t('emotion_report_section8_key_p4')}}</p>
        </div>
        
        <div class="reference">
            <p>5. Linehan, M. M. (2014). "DBT Skills Training Manual." Guilford Publications.</p>
            <p class="reference-note">- {{$t('emotion_report_section8_key_p5')}}</p>
        </div>
      </div>

      <div class="section footer">
        <div class="notes">
          <p>{{$t('emotion_report_notes_key')}}</p>
            <ol>
                <li>{{$t('emotion_report_notes_key_li1')}}</li>
                <li>{{$t('emotion_report_notes_key_li2')}}</li>
                <li>{{$t('emotion_report_notes_key_li3')}}</li>
            </ol>
        </div>

        <div class="signature">
            <p>{{$t('report_doctor_key')}} {{$t('colon_key')}}</p>
            <p>{{$t('report_date_key')}} {{$t('colon_key')}}</p>
            <p>{{$t('report_hospital_seal')}} {{$t('colon_key')}}</p>
        </div>
      </div>

    </div>
    <div v-else>
      No results available. Please perform an analysis first.
    </div>
    <div class="button-container">
      <button @click="exportToPDF" class="back-btn">{{$t('print_report_key')}}</button>
      <button @click="goBack" class="back-btn">{{$t('return_key')}}</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useEmotionStore } from '@/stores/emotionStore'
import { storeToRefs } from 'pinia'
import FaceEmotion from '@/components/FaceEmotion.vue'
import { useI18n } from 'vue-i18n';
import { api } from '@/api';

const { t } = useI18n();

const router = useRouter()
const emotionStore = useEmotionStore()
const { analysisResults, hasResults } = storeToRefs(emotionStore)

const screen_idx = ref("")
const screen_time = ref("")
const screen_name = ref("")
const valence_str = ref("")
const arousal_str = ref("")
const valence_list = ref([])
const arousal_list = ref([])


const props = defineProps({
  en_session_id: String,
});

const goBack = () => {
  emotionStore.resetState()
  router.push({ name: 'Emotion' })
}
const exportToPDF = () => {
  // 调用android注入的接口
  if (typeof android !== 'undefined') {
      android.printPage();
  } else {
      window.print(); // 降级方案：使用浏览器默认打印
  }
}

const preloadImages = () => {
  analysisResults.value.forEach(result => {
    new Image().src = result.orig_blob_url
    new Image().src = result.mesh_blob_url

    screen_idx.value = result.screening_idx
    screen_time.value = result.screening_time
    valence_list.value.push(result.valence_arousal.valence_value)
    arousal_list.value.push(result.valence_arousal.arousal_value)
  })

  const valence_sum = valence_list.value.reduce((accumulator, currentValue) => {
      return accumulator + currentValue;
  }, 0);
  const valence_mean = valence_sum / valence_list.value.length;
  const arousal_sum = arousal_list.value.reduce((accumulator, currentValue) => {
      return accumulator + currentValue;
  }, 0);
  const arousal_mean = arousal_sum / arousal_list.value.length;
  if (valence_mean > 0) {
    valence_str.value = t('emotion_valence_positive_key')
  } else {
    valence_str.value = t('emotion_valence_negative_key')
  }
  if (arousal_mean > 0) {
    arousal_str.value = t('emotion_arousal_high_key')
  } else {
    arousal_str.value = t('emotion_arousal_low_key')
  }
}

// 打印每个 face 的详细信息
const logFaceDetails = () => {
  console.log('Face details:')
  analysisResults.value.forEach((result, index) => {
    console.log(`Face ${index + 1}:`, result)
    console.log(`Expression: ${result.expression}`)
    console.log(`Confidence: ${result.confidence.toFixed(4)}`)
    console.log('Action Unit Information:')
    Object.entries(result.au_infos).forEach(([key, value]) => {
      console.log(`  ${key}: ${(value * 100).toFixed(2)}%`)
    })
    console.log('---')
  })
}

const getEmotionListData = async () => {

  try {
    const response = await api.get('/api/v1/screening/get_emotion_report/' + props.en_session_id);

    if (response.status != 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = response.data;

    if (data.code !== 0) {
      throw new Error(`API error! code: ${data.code}`);
    }

    return data.data.emotion_list;
  } catch (error) {
    console.error('There was a problem with the fetch operation:', error);
    throw error;
  }
}

// 在组件挂载时和 results 更新时打印 face 详细信息
onMounted( async () => {
  analysisResults.value = await getEmotionListData()
  hasResults.value = analysisResults.value.length > 0
  preloadImages()
  // logFaceDetails()
})
watch(analysisResults, logFaceDetails, { deep: true })
</script>

<style scoped>

.emotion-report {
  max-width: 1000px;
  width: 100vw;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  color: #333;
  font-size: 1.2rem;
}

.face-emotion-container {
  margin-bottom: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: white;
}

/* 标题样式 */
h1 {
    font-size: 2.4rem;
    font-weight: 300;
    letter-spacing: 0.1em;
    margin-bottom: 2rem;
    text-align: center;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e6e6e6;
    color: #333;
    margin-bottom: 30px;
}

h2 {
    font-size: 1.8rem;
    font-weight: 300;
    letter-spacing: 0.05em;
    color: #666;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

h3 {
    font-size: 1.4rem;
    font-weight: 400;
    margin: 2rem 0;
    color: #707070;
}

h4 {
    font-size: 1.2rem;
    font-weight: 400;
    margin: calc(2rem * 0.5) 0;
    color: #707070;
}

p {
    margin-bottom: 1rem;
    line-height: 1.8;
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 20px;
  padding-bottom: 20px;
  gap: 10px;
}

.back-btn {
  min-width: 120px; /* 设置最小宽度 */
  padding: 12px 24px;
  font-size: 16px;
  background-color: #17d7ea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


.back-btn:hover {
  background-color: #14bccf;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 480px) {
  .back-btn {
    min-width: 100px;
    padding: 10px 20px;
  }
}

.basic-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 2rem 0;
    padding: 2rem;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.basic-info div {
    padding: 1rem;
}

/* 内容区块样式 */
.section {
    margin: calc(2rem * 2) 0;
    padding: 2rem;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    text-align: left;
    
}

/* 列表样式 */
ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 2rem;
}

li {
    margin-bottom: 0.5rem;
    color: #707070;
}

/* 情绪象限样式 */
.quadrant-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin: 2rem 0;
}

.quadrant {
    padding: 2rem;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
}

/* 建议卡片样式 */
.suggestion-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.suggestion-item {
    padding: 2rem;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
}

/* 参考文献样式 */
.reference {
    padding: 1rem 0;
    border-bottom: 1px solid #e6e6e6;
}

.reference:last-child {
    border-bottom: none;
}

/* 脚注样式 */
.footer {
    margin-top: calc(2rem * 3);
    padding-top: 2rem;
    border-top: 1px solid #e6e6e6;
    text-align: center;
    color: #707070;
    font-size: 0.9rem;
    text-align: left;
}

.signature {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e6e6e6;
}

/* 应急联系方式样式 */
.emergency-contacts {
    background-color: #fff8f8;
    padding: 2rem;
    border-radius: 4px;
    margin: 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {

    .container {
        padding: 0 1rem;
    }

    .basic-info,
    .quadrant-grid {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.6rem;
    }

    h3 {
        font-size: 1.2rem;
    }
}

@media print {
  .button-container {
    display: none;
  }
  .emotion-analysis {
    display: none;
  }
  .emotion-sugguestion {
    display: none;
  }
  .emotion-imporvement {
    display: none;
  }
  .emotion-future-plan {
    display: none;
  }
  .emotion-tips {
    display: none;
  }
  .emergency-contacts {
    display: none;
  }
  .emotion-reference {
    display: none;
  }

}
</style>