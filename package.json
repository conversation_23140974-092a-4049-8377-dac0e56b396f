{"name": "mpd_app_h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "build": "vite build", "preview": "vite preview", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@mediapipe/tasks-vision": "0.10.3", "@sentry/vite-plugin": "^3.2.4", "@sentry/vue": "^9.10.1", "axios": "^1.7.7", "chart.js": "^4.4.9", "echarts": "^5.5.1", "element-plus": "^2.7.7", "fft-js": "^0.0.12", "fft.js": "^4.0.4", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.2", "marked": "^14.1.1", "mathjs": "^13.1.1", "microsoft-cognitiveservices-speech-sdk": "^1.40.0", "pinia": "^2.1.7", "sass": "^1.79.4", "sass-loader": "^16.0.2", "vue": "^3.4.29", "vue-chartjs": "^5.3.1", "vue-i18n": "^9.13.1", "vue-router": "^4.4.0", "wavesurfer": "^1.3.4", "@vueuse/core": "^10.11.0", "js-md5": "^0.8.0", "qs": "^6.12.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/types": "^7.27.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/test-utils": "^2.4.6", "@vue/vue3-jest": "^27.0.0", "babel-jest": "^27.0.0", "jest": "^27.0.0", "jest-environment-jsdom": "^27.0.0", "jsdom": "^25.0.1", "postcss-px-to-viewport": "^1.1.1", "vite": "^5.3.1"}}