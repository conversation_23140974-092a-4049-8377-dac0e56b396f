<template>
	<div class="init-container">
		<div class="init-container-title">{{ $t("hrv_title_wait_key") }}</div>
		<div class="init-container-text">
			{{ $t("hrv_init_tips") }}
		</div>
		<img :src="imageBack" alt="icon" class="init-container-icon" />

		<div class="init-container-btn" @click="goTest">
			{{ $t("hrv_init_ready") }}
		</div>
	</div>
</template>

<script setup>
import {
	isAndroidEnv,
	notifyAndroidReady,
	getAndroidInitParams,
} from "@/utils/android"
import { getCurrentLanguage } from "@/utils/i18n"
import { ref, defineEmits, defineProps, computed, onMounted } from "vue"
import { useRouter } from "vue-router"
import tipBackEn from "@/assets/hrv/hrvinit_tip_back_en.png"
import tipBackZh from "@/assets/hrv/hrvinit_tip_back_zh.png"

const router = useRouter()
const imageBack = computed(() => {
	return getCurrentLanguage().includes("zh") ? tipBackZh : tipBackEn
})
const goTest = () => {
	try {
		const params = new URLSearchParams(window.location.search)
		const urlParam = params.toString()
		router.push("/hrvtest?" + urlParam)
		notifyAndroidReady()
	} catch (e) {
		console.log(e, "e")
	}
}

</script>

<style scoped lang="scss">
.test {
	width: 200px;
	height: 200px;
}
.init-container {
	width: 100vw;
	height: 100vh;
	// background-size: 100% 100%;
	// background: url("https://img3.airdoc.com/staticResources/data/static/js/hrvinit_back.png");
	font-weight: 500;
	font-size: 2.5rem;
	color: #333333;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	border-radius: 2.2rem;
	background: linear-gradient(to bottom, #ffffff, #f1dada);
	&-title {
		font-weight: bold;
		font-size: 3.25rem;
		color: #333333;
		margin-bottom: 2.3rem;
	}
	&-text {
		width: 90%;
		text-align: center;
	}
	&-icon {
		width: 64rem;
		object-fit: cover;
		margin-top: 1.97rem;
		margin-bottom: 2.03rem;
	}
	&-btn {
    width: 38rem;
    height: 7.48rem;
    background: #eb4e89;
    border-radius: 3.44rem;
    font-weight: 500;
    font-size: 3rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 5.4rem;
	}
}
</style>
