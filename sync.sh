branch=$(git rev-parse --abbrev-ref HEAD)
commit=$(git rev-parse --short HEAD)
commit_count=$(git rev-list --count HEAD)
echo "generate version version=${branch}.${commit_count}.${commit}"
echo "{\"version\":\"${branch}.${commit_count}.${commit}\"}" > public/version.json

echo "build package"
yarn build

echo "sync workspace"
rsync -azP --exclude .DS_Store --delete dist/ llm@172.210.44.237:/home/<USER>/web/copilot

ssh llm@172.210.44.237 << EOF
    echo "Deploying files to /var/www/html/"
    sudo cp -r ~/web/copilot/* /var/www/html/
    sudo chmod -R 755 /var/www/html/*
    echo "Deployment completed."
EOF
echo "{\"version\":\"${branch}.${commit_count}.${commit}\"}  deployed"
