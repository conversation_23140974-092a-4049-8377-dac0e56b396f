<template>
	<div class="tips-box" v-if="visible">
		<img
			class="tips-box-icon"
			src="@/assets/hrv/hrvinit/hrvinit_warning.png"
			alt="icon"
		/>
		<div class="tips-box-content">{{ content }}</div>
	</div>
</template>
<script setup>
import { defineProps } from "vue"
const props = defineProps({
	content: {
		type: String,
		default: "",
		required: true,
	},
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
})
</script>
<style lang="scss" scoped>
.tips-box {
	position: absolute;
	top: 1rem;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1000;
  padding: 1rem;
	min-width: 35.06rem;
	min-height: 10rem;
	background: rgba(235, 78, 137, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
	border-radius: 1.75rem 1.75rem 1.75rem 1.75rem;
  &-icon {
    width: 4rem;
    height: 4rem;
  }
	&-content {
		font-size: 2rem;
		color: #fff;
	}
}
</style>
