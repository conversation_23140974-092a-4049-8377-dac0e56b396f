
export const saveImageDataAsImage = (imageData, baseFileName = 'image') => {
  // 创建一个新的 canvas 元素
  const canvas = document.createElement('canvas');
  canvas.width = imageData.width;
  canvas.height = imageData.height;

  // 将 imageData 绘制到 canvas 上
  const ctx = canvas.getContext('2d');
  ctx.putImageData(imageData, 0, 0);

  // 将 canvas 转换为图片数据 (PNG 或 JPEG)
  const dataURL = canvas.toDataURL('image/png'); // 或者 'image/jpeg'

  // 获取当前时间，格式化为 yyyymmdd_hhmmss
  const now = new Date();
  const timestamp = now.toISOString().replace(/[-T:.Z]/g, '').slice(0, 15); // 转化为 yyyymmdd_hhmmss 格式

  // 拼接文件名，添加时间戳后缀
  const fileName = `${baseFileName}_${timestamp}.png`;  // 拼接为 'image_yyyymmdd_hhmmss.png'

  // 创建一个 <a> 元素，用于自动下载图片
  const link = document.createElement('a');
  link.href = dataURL;
  link.download = fileName;

  // 自动触发下载
  document.body.appendChild(link);  // 必须将元素添加到文档中
  link.click();
  document.body.removeChild(link);  // 下载后移除元素
};

export function saveVideoDataAsImage(video, baseFileName = 'video') {

  if (video.value && video.value.readyState === video.value.HAVE_ENOUGH_DATA) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    canvas.width = video.value.videoWidth;
    canvas.height = video.value.videoHeight;

    // 将视频帧绘制到 Canvas 上
    context.drawImage(video.value, 0, 0, canvas.width, canvas.height);

    // 将 Canvas 的内容转换为 Data URL
    const dataURL = canvas.toDataURL('image/png');

    // 获取当前时间，格式化为 yyyymmdd_hhmmss
    const now = new Date();
    const timestamp = now.toISOString().replace(/[-T:.Z]/g, '').slice(0, 15); // 转化为 yyyymmdd_hhmmss 格式

    // 拼接文件名，添加时间戳后缀
    const fileName = `${baseFileName}_${timestamp}.png`;  // 拼接为 'image_yyyymmdd_hhmmss.png'

    // 创建一个 <a> 元素，用于自动下载图片
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = fileName;

    // 自动触发下载
    document.body.appendChild(link);  // 必须将元素添加到文档中
    link.click();
    document.body.removeChild(link);  // 下载后移除元素
  }
}

export function saveMatAsImage(mat, baseFileName = 'video') {
  // 创建 Canvas 元素
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  // 设置 Canvas 大小为 Mat 矩阵的宽高
  canvas.width = mat.cols;
  canvas.height = mat.rows;

  // 将 Mat 转换为 ImageData
  const imageData = new ImageData(new Uint8ClampedArray(mat.data), mat.cols, mat.rows);

  // 将 ImageData 绘制到 Canvas 上
  context.putImageData(imageData, 0, 0);

  // 将 Canvas 内容转换为图片数据 URL
  const dataURL = canvas.toDataURL('image/png');

  // 获取当前时间，格式化为 yyyymmdd_hhmmss
  const now = new Date();
  const timestamp = now.toISOString().replace(/[-T:.Z]/g, '').slice(0, 15); // 转化为 yyyymmdd_hhmmss 格式

  // 拼接文件名，添加时间戳后缀
  const fileName = `${baseFileName}_${timestamp}.png`;  // 拼接为 'image_yyyymmdd_hhmmss.png'

  // 创建一个 <a> 元素，用于自动下载图片
  const link = document.createElement('a');
  link.href = dataURL;
  link.download = fileName;

  // 自动触发下载
  document.body.appendChild(link);  // 必须将元素添加到文档中
  link.click();
  document.body.removeChild(link);  // 下载后移除元素
}