<template>
	<div class="video-wrapper">
		<div class="video-section">
			<video ref="video" autoplay class="mirror"></video>
			<canvas ref="canvas" class="facemesh"></canvas>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, watchEffect, onBeforeUnmount } from "vue"
import { createFaceLandmarker, initOpenCV } from "../../utils/vision-utils.js"
import {
	saveImageDataAsImage,
	saveMatAsImage,
} from "../../utils/image-utils.js"
import { DrawingUtils, FaceLandmarker } from "@mediapipe/tasks-vision"
import { defineEmits } from "vue"
import { useHrvStore } from "@/stores/hrvStore.js"
import {
	FACE_NOT_DETECTED,
	FACE_TOO_SMALL,
	FACE_TOO_LARGE,
	FACE_APPROPRIATE,
	FACE_TOO_CLOSE_TO_EDGE,
} from "../../constants/faceStatusConstants.js"
import { StreamUploader } from "../../utils/record_video.js"
import { api } from "@/api"

const hrvStore = useHrvStore()
const showGrid = false
const video = ref(null)
const canvas = ref(null)
let timeDiffArray = ref([])
let rgbMeanArray = ref([])
let fps = ref(0)
const uploader = ref(null)
const faceToken = ref("")
defineExpose({
	timeDiffArray,
	rgbMeanArray,
	fps,
	uploader,
	faceToken,
})

const props = defineProps({
	stage: Number,
})

const faceStatus = ref(FACE_NOT_DETECTED) // 存储当前的人脸状态
const emit = defineEmits(["faceStatus"]) // 定义 emit 方法

watchEffect(() => {
	emit("faceStatus", faceStatus.value)
})

let faceLandmarker = null
let mediaPipeInitialized = false
let lastProcessTime = 0
let frameCount = 0

let videoStream = null

onMounted(async () => {
	initCameraModel()
})
const initCameraModel = async () => {
	try {
		setupCamera()
		console.log("Initializing vision tasks and OpenCV in parallel...")

		// Run both tasks in parallel
		const [landmarker] = await Promise.all([
			createFaceLandmarker(),
			initOpenCV(),
		])

		// Assign faceLandmarker after initialization
		faceLandmarker = landmarker
		mediaPipeInitialized = true
		hrvStore.setLoadModelFlag(true)
		console.log("Both vision tasks and OpenCV initialized successfully.")
	} catch (error) {
		hrvStore.setLoadModelFlag(false)
		console.error("Error initializing vision tasks or OpenCV:", error)
	}

	console.log("Setup camera...")
}
onBeforeUnmount(() => {
	if (videoStream) {
		videoStream.getTracks().forEach((track) => {
			track.stop() // 停止所有轨道
			track.enabled = false // 禁用轨道
		})
		videoStream = null
	}
	if (video.value) {
		video.value.srcObject = null
		video.value.removeEventListener("loadeddata", predictWebcam) // 移除事件监听器
	}
})
const setupCamera = () => {
	// 确保之前的资源已释放
	if (videoStream) {
		videoStream.getTracks().forEach((track) => track.stop())
		videoStream = null
	}
	if (video.value) {
		video.value.srcObject = null
	}
	navigator.mediaDevices.enumerateDevices().then((devices) => {
		const cameras = devices.filter((device) => device.kind === "videoinput")
		console.log(cameras) // 会包含摄像头设备的 label 和 deviceId
	})
	navigator.mediaDevices
		.getUserMedia({
			video: {
				facingMode: "user",
			},
		})
		.then((stream) => {
			videoStream = stream
			video.value.srcObject = stream
			video.value.addEventListener("loadeddata", predictWebcam)

			console.log("开始设置摄像机")
		})
		.catch((error) => {
			console.error("Camera access failed: ", error)
		})
		.finally(() => {
			console.log("结束设置摄像机")
		})
}

const predictWebcam = async () => {
	const predict = async () => {
		if (!mediaPipeInitialized || !canvas.value) return

		const results = await faceLandmarker.detectForVideo(
			video.value,
			performance.now()
		)

		const canvasCtx = canvas.value.getContext("2d")
		const canvasWidth = canvas.value.width
		const canvasHeight = canvas.value.height

		canvas.value.width = video.value.videoWidth
		canvas.value.height = video.value.videoHeight

		canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight)
		canvasCtx.drawImage(video.value, 0, 0, canvasWidth, canvasHeight)

		frameCount++
		const sample_count = 30
		if (frameCount % sample_count === 0) {
			const lastTimeDiffs = timeDiffArray.value.slice(-sample_count)
			const sum = lastTimeDiffs.reduce((acc, val) => acc + val, 0)
			fps.value = 1000 / (sum / sample_count)
			// console.log("total time = " + sum.toFixed(2) + " ms, fps = " +  fps.value.toFixed(2));
		}

		// 判断人脸状态
		if (results.faceLandmarks && results.faceLandmarks.length > 0) {
			const landmarks = results.faceLandmarks[0]
			processFrame(canvas, landmarks)

			// 获取人脸包含的最小外接矩形的顶点
			const xValues = landmarks.map(
				(point) => point.x * canvas.value.width
			)
			const yValues = landmarks.map(
				(point) => point.y * canvas.value.height
			)
			const minX = Math.min(...xValues)
			const maxX = Math.max(...xValues)
			const minY = Math.min(...yValues)
			const maxY = Math.max(...yValues)

			// 计算人脸的矩形宽度、高度及面积
			const faceWidth = maxX - minX
			const faceHeight = maxY - minY
			const faceArea = faceWidth * faceHeight

			// 获取整个图像的面积
			const imageArea = canvas.value.width * canvas.value.height

			const minFaceArea = imageArea / 10 // 1/10图像面积的阈值
			const maxFaceArea = imageArea * 0.8 // 0.8图像面积的阈值
			const minBoundaryDistance = 10 // 人脸距离边界的最小像素距离阈值

			// 判断人脸状态
			if (
				minX <= minBoundaryDistance ||
				minY <= minBoundaryDistance ||
				canvas.value.width - maxX <= minBoundaryDistance ||
				canvas.value.height - maxY <= minBoundaryDistance
			) {
				faceStatus.value = FACE_TOO_CLOSE_TO_EDGE
			} else if (faceArea < minFaceArea) {
				faceStatus.value = FACE_TOO_SMALL
			} else if (faceArea > maxFaceArea) {
				faceStatus.value = FACE_TOO_LARGE
			} else {
				faceStatus.value = FACE_APPROPRIATE
			}
			if (
				faceStatus.value == FACE_APPROPRIATE &&
				frameCount % (sample_count * 5) === 0
			) {
				captureImage(canvas.value)
			}

			if (showGrid) {
				const drawingUtils = new DrawingUtils(canvasCtx)
				drawingUtils.drawConnectors(
					landmarks,
					FaceLandmarker.FACE_LANDMARKS_TESSELATION,
					{
						color: "#C0C0C070",
						lineWidth: 1,
					}
				)
			}
		} else {
			faceStatus.value = FACE_NOT_DETECTED
		}
	}

	setInterval(predict, 1000 / 30) // 每秒30次预测
	// setInterval(predict, 1000 / 10) // 每秒30次预测
}

const captureImage = async (canvas) => {
	if (props.stage != 2) {
		return
	}

	if (faceToken.value != "") {
		return
	}

	canvas.toBlob(
		async (blob) => {
			if (!blob) {
				console.error("Failed to create blob from canvas.")
				return
			}
			const formData = new FormData()
			formData.append("file", blob, "face.jpg") // 确保字段名与后端期望的一致
			try {
				const response = await api.post(
					"/api/detection/faceId",
					formData,
					{
						headers: {
							"Content-Type": "multipart/form-data",
						},
					}
				)
				if (response.data.code == 0) {
					faceToken.value = response.data.data.entityId
				}
			} catch (error) {
				console.error(
					"There was a problem with the fetch operation:",
					error
				)
			}
		},
		"image/jpeg",
		0.8
	)
}

const processFrame = (canvas, landmarks) => {
	if (props.stage != 2) {
		return
	}

	const lower_face = [200, 431, 411, 340, 349, 120, 111, 187, 211]
	const points = lower_face.map((index) => ({
		x: Math.round(landmarks[index].x * canvas.value.width),
		y: Math.round(landmarks[index].y * canvas.value.height),
	}))

	const ctx = canvas.value.getContext("2d")
	const imageData = ctx.getImageData(
		0,
		0,
		canvas.value.width,
		canvas.value.height
	)

	const src = cv.matFromImageData(imageData)
	const mask = new cv.Mat(src.rows, src.cols, cv.CV_8UC1, new cv.Scalar(0))

	const roiPoints = new cv.MatVector()
	roiPoints.push_back(
		cv.matFromArray(
			points.length,
			1,
			cv.CV_32SC2,
			points.flatMap((p) => [p.x, p.y])
		)
	)
	cv.fillPoly(mask, roiPoints, new cv.Scalar(255, 255, 255))

	const dst = new cv.Mat()
	src.copyTo(dst, mask)

	let rgbMean = cv.mean(dst, mask)

	const prevProcessTime = lastProcessTime
	lastProcessTime = performance.now()
	if (lastProcessTime > 0) {
		const timeDiff = lastProcessTime - prevProcessTime
		rgbMeanArray.value.push(rgbMean)
		timeDiffArray.value.push(timeDiff)
	}

	roiPoints.delete()
	mask.delete()
	src.delete()
	dst.delete()
}
</script>

<style scoped>
.video-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: auto;
}

.video-section {
	position: relative;
	height: 100%;
	max-width: 900px;
}

.video-section video {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 15px;
	overflow: hidden;
}

.mirror {
	transform: scaleX(-1);
}

.facemesh {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	transform: scaleX(-1);
	border-radius: 15px;
	overflow: hidden;
}
</style>
