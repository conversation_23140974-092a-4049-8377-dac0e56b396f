// src/utils/HRCalculator.js
import DigitalFilter from './DigitalFilter';
import FFT from 'fft.js';

class HRCalculator {
  constructor(updateInterval = 30, winSize = 1800) {
    this.counter = 0;
    this.updateInterval = updateInterval;
    this.rrIntervals = [];

    this.lastBpm = 0;
    this.timeDomainHRV = NaN;
    this.frequencyDomainHRV = NaN;

    let a = [1.000000, -1.561018, 0.641352];
    let b = [0.020083,  0.040167, 0.020083];
    this.digital_filter = new DigitalFilter(b, a);
  }

  updateRRIntervals(peaks, ts) {
    if (peaks.length < 2) {
      return NaN;
    }

    let rrIntervals = [];
    for (let i = 0; i < peaks.length - 1; i++) {
      let cumulativeTime = 0;
      for (let j = peaks[i]; j < peaks[i + 1]; j++) {
        cumulativeTime += ts[j];
      }
      rrIntervals.push(cumulativeTime);
    }

    // console.log(rrIntervals);
    this.rrIntervals = rrIntervals;
  }

  calculateBPM(rrIntervals) {
    if (rrIntervals.length < 2) return 0;

    const meanRR = rrIntervals.reduce((sum, rr) => sum + rr, 0) / rrIntervals.length;
    return 60000 / meanRR;
  }

  calculateTimeDomainHRV(rrIntervals) {
    if (rrIntervals.length < 2) return { meanRR: 0, sdnn: 0, rmssd: 0, pnn50: 0 };

    const meanRR = rrIntervals.reduce((sum, rr) => sum + rr, 0) / rrIntervals.length;
    const sdnn = Math.sqrt(rrIntervals.reduce((sum, rr) => sum + Math.pow(rr - meanRR, 2), 0) / rrIntervals.length);
    const diffRR = rrIntervals.slice(1).map((rr, i) => rr - rrIntervals[i]);
    const rmssd = Math.sqrt(diffRR.reduce((sum, diff) => sum + diff * diff, 0) / diffRR.length);
    const nn50 = diffRR.filter(diff => Math.abs(diff) > 50).length;
    const pnn50 = (nn50 / diffRR.length) * 100;
    return { meanRR, sdnn, rmssd, pnn50 };
  }

  calculateFrequencyDomainHRV(rrIntervals) {
    if (rrIntervals.length < 2) {
      return { lf: 0, hf: 0, lf_hf_ratio: 0 };
    }

    // 去除均值，得到归一化的 RR 间隔
    const meanRR = rrIntervals.reduce((sum, rr) => sum + rr, 0) / rrIntervals.length;
    const rrNormalized = rrIntervals.map(rr => rr - meanRR);

    // 自动估算采样率：采样率 = 每秒心跳次数 = 1000 / 平均 RR 间隔
    const samplingRate = 1000 / meanRR;

    // 确定 FFT 的大小 N，最近的 2 的幂次
    const N = Math.pow(2, Math.ceil(Math.log2(rrNormalized.length)));
    // console.log("fft(N), N=", N);

    // 填充零到 rrNormalized 数组，使其长度为 N
    const paddedRR = new Array(N).fill(0);
    for (let i = 0; i < rrNormalized.length; i++) {
      paddedRR[i] = rrNormalized[i];
    }

    // 创建复数数组，并执行 FFT 变换
    const fft = new FFT(N);
    const frequencies = fft.createComplexArray();
    fft.realTransform(frequencies, rrNormalized);

    // console.log("fft(frequencies), frequencies=", frequencies);

    // 计算频率步长：freqStep = 采样率 / FFT 点数
    const freqStep = samplingRate / N;

    // 频率范围：LF 0.04 - 0.15 Hz，HF 0.15 - 0.4 Hz
    const LF_RANGE = [0.04, 0.15];
    const HF_RANGE = [0.15, 0.4];

    // 计算 LF 和 HF 的功率
    const lfPower = this.calculatePower(frequencies, LF_RANGE, freqStep);
    const hfPower = this.calculatePower(frequencies, HF_RANGE, freqStep);

    // 计算 LF/HF 比值
    const lfHfRatio = hfPower > 0 ? lfPower / hfPower : NaN;

    return {
      lf: lfPower,
      hf: hfPower,
      lf_hf_ratio: lfHfRatio
    };
  }

  calculatePower(frequencies, range, freqStep) {
    const [minFreq, maxFreq] = range;
    let power = 0;

    for (let i = 0; i < frequencies.length / 2; i++) {
      const freq = i * freqStep;
      if (freq >= minFreq && freq <= maxFreq) {
        const real = frequencies[2 * i];  // 实部
        const imag = frequencies[2 * i + 1];  // 虚部
        power += Math.sqrt(real * real + imag * imag);  // 计算复数的模
      }
    }

    return power;
  }

  getSamplingRate(ts) {
    const average = ts.reduce((sum, value) => sum + value, 0) / ts.length;
    return 1000.0 / average;
  }

  findPeaks(data, mindist) {
    // Basic peak finding function. You can use a more sophisticated algorithm if needed.
    const peaks = [];
    for (let i = 1; i < data.length - 1; i++) {
      if (data[i] > data[i - 1] && data[i] > data[i + 1] && (peaks.length === 0 || i - peaks[peaks.length - 1] >= mindist)) {
        peaks.push(i);
      }
    }
    // console.log("peaks=", peaks);
    return peaks;
  }

  update(ts_in, vs_in) {
    this.counter++;
    if (this.counter >= this.updateInterval) {
      this.counter = 0;

      const ts = ts_in.slice(-this.winSize);
      const vs = vs_in.slice(-this.winSize);

      const filteredVs = vs.map(v => this.digital_filter.process(v));
      const peaks = this.findPeaks(filteredVs, Math.floor(this.getSamplingRate(ts) * 0.35));
      this.updateRRIntervals(peaks, ts);

      // console.log("this.rrIntervals.length=", this.rrIntervals.length);
      this.lastBpm = this.calculateBPM(this.rrIntervals);

      // console.log("this.rrIntervals.length=", this.rrIntervals.length);
      this.timeDomainHRV = this.calculateTimeDomainHRV(this.rrIntervals);

      // console.log("this.rrIntervals.length=", this.rrIntervals.length);
      this.frequencyDomainHRV = this.calculateFrequencyDomainHRV(this.rrIntervals);
    }

    return {
      bpm: this.lastBpm,
      timeDomainHRV: this.timeDomainHRV,
      frequencyDomainHRV: this.frequencyDomainHRV
    };
  }
}

export default HRCalculator;
