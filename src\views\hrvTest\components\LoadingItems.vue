<template>
	<div
		class="hrvInit-content-load"
		:style="{
			fontSize: language.includes('en') ? '1.7rem' : '2.25rem',
      width: language.includes('en') ? '22rem' : '',
		}"
		v-if="stage == RecordStage.DETECT"
	>
		<div
			class="hrvInit-content-load-item"
			v-for="(item, index) in list"
			:key="index"
		>
			<img :src="item.imageSrc" alt="icon" />
			<div class="hrvInit-content-load-item-text">
				{{ item.label }}
			</div>
			<img
				v-if="item.flag"
				src="@/assets/hrv/hrvinit/hrvinit_finshed.png"
				alt="icon"
			/>

			<div v-else class="loading-spinner"></div>
		</div>
	</div>
</template>

<script setup>
import { defineProps, computed } from "vue"
import { getCurrentLanguage } from "@/utils/i18n"

import { RecordStage } from "@/constants/recordStage"
const props = defineProps({
	list: {
		type: Array,
		required: true,
	},
	stage: {
		type: Number,
		default: 0,
	},
})
const language = computed(() => getCurrentLanguage())
</script>

<style scoped lang="scss">
.hrvInit-content-load {
	font-size: 2.25rem;
	&-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 500;
		color: #666666;
		img {
			width: 2.5rem;
			object-fit: cover;
		}
		img:nth-child(1) {
			margin-right: 1rem;
		}
		img:nth-child(2) {
			margin-left: 0.5rem;
		}
    &-text {
      width:70%;
    }
		.loading-spinner {
			width: 2rem;
			height: 2rem;
			border: 1px dashed #ffa500;
			border-radius: 50%;
			animation: spin 1s linear infinite;
			border-top-color: transparent; // 顶部透明，形成不完整圆
			box-sizing: border-box;
			margin-left: 0.5rem;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}
	}
}
.hrvInit-content-load__en {
	font-size: 1.7rem;
}
</style>
