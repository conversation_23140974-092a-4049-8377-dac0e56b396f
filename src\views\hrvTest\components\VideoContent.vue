<template>
	<div
		class="hrvInit-content-video"
		:class="
			stage === RecordStage.IMAGE_DISPLAY ? 'video-small' : 'video-big'
		"
	>
		<!-- 变小屏同时未收起倒计时时展示 -->
		<template
			v-if="stage === RecordStage.IMAGE_DISPLAY && !retractCountdown"
		>
			<div class="hrvInit-content-video-count">
				<img
					class="hrvInit-content-video-count-icon"
					src="@/assets/hrv/hrvinit/hrvinit_stopwatch.png"
					alt="icon"
				/>
				<span>
					{{ totalTimeLeft }}<span class="count-prefix">s</span>
				</span>
			</div>
			<div
				class="hrvInit-content-video-tip"
				:style="{
					fontSize: language.includes('en') ? '1.7rem' : '2.25rem',
				}"
			>
				<div class="triangle"></div>
				{{ $t("hrv_test_imagebox_tip_key") }}
			</div>
		</template>
		<div
			class="hrvInit-content-video-persion"
			:class="[
				stage === RecordStage.IMAGE_DISPLAY
					? 'hrvInit-content-video-persion__small'
					: 'hrvInit-content-video-persion__big',
				retractCountdown
					? 'hrvInit-content-video-persion__retract'
					: '',
			]"
		>
			<!-- <img
				:src="
					stage === RecordStage.IMAGE_DISPLAY
						? persionSmall
						: persionBig
				"
				class="hrvInit-content-video-persion-back"
				alt="icon"
			/> -->
			<VideoWrapper
				@faceStatus="handleFaceStatus"
				ref="videoWrapperRef"
				:stage="stage"
				class="hrvInit-content-video-persion-content"
			/>
		</div>
	</div>
</template>

<script setup>
import { defineProps, computed, onMounted, defineExpose, ref } from "vue"
import { RecordStage } from "@/constants/recordStage"
import VideoWrapper from "@/components/hrv/VideoWrapper.vue"
import TipsBox from "./TipsBox.vue"
import persionBig from "@/assets/hrv/hrvinit/hrvinit_persion_big.png"
import persionSmall from "@/assets/hrv/hrvinit/hrvinit_persion_small.png"
import { getCurrentLanguage } from "@/utils/i18n"

const videoWrapperRef = ref(null)
const language = computed(() => getCurrentLanguage())

const emit = defineEmits(["faceStatus"])
const props = defineProps({
	retractCountdown: {
		type: Boolean,
		required: true,
	},
	stage: {
		type: Number,
		required: true,
	},
	errorPostureTips: {
		type: String,
		required: true,
	},
	isShowErrorPostureTips: {
		type: Boolean,
		required: true,
	},
	totalTimeLeft: {
		type: Number,
		required: true,
	},
})
const handleFaceStatus = (status) => {
	emit("faceStatus", status)
}

defineExpose({
	faceView: videoWrapperRef,
})
</script>

<style scoped lang="scss">
.hrvInit-content-video {
	&-tip {
		width: 15rem;
		min-height: 4.44rem;
		padding: 0.5rem;
		background: #ffffff;
		border-radius: 0.63rem 0.63rem 0.63rem 0.63rem;
		border: 0.06rem solid rgba(235, 78, 137, 0.5);
		font-weight: 500;
		font-size: 2.25rem;
		color: #666666;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		.triangle {
			width: 0;
			height: 0;
			border-top: 0.8rem solid transparent;
			border-bottom: 0.8rem solid transparent;
			border-right: 0.8rem solid #eb4e89;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			left: -0.4rem;
			z-index: 1;
		}
	}
	&-count {
		width: 13.75rem;
		height: 13.75rem;
		background: #ffffff;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		border: 0.13rem solid rgba(235, 78, 137, 0.27);
		font-weight: 600;
		font-size: 4.5rem;
		color: #666666;
		margin-bottom: 16rem;
		.count-prefix {
			font-size: 2rem;
		}
		&-icon {
			width: 4rem;
			height: 4rem;
		}
	}
	&-persion {
		&__big {
			width: 65rem;
			// height: 43rem;
			border-radius: 1.75rem;
			margin: 0 2rem 0 2rem;
			position: relative;
		}
		&__small {
			width: 17rem;
			// height: 15rem;
			position: relative;
		}
		&__retract {
			position: absolute;
			bottom: 0.8rem;
			right: -0.4rem;
		}
		&-content {
			width: 100%;
			height: 100%;
		}
		&-back {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}
	}
}
.video-small {
	height: 100vh;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-direction: column;
	background: black;
	padding-top: 8.8rem;
	box-sizing: border-box;
	position: absolute;
	right: 10px;
	padding-bottom: 0.63rem;
}
.video-big {
}
</style>
