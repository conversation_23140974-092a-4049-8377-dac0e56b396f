import { ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import { api } from "@/api"
import {
	getAndroidVersion,
	goBackAppHomePage,
	isAndroidEnv,
	notifyAndroidOnFinish,
	notifyPrintPdf,
} from "@/utils/android.js"

export function useHrvReport() {
	const hrv_data = ref(null)
	const showHrvGraphic = ref(null)
	const showHrvBottomSignature = ref(null)
	const basicInfo = ref({})
	const RRIntervals = ref([])
	const hrvInfo = ref({})
	const nerveInfo = ref({})
	const pressureInfo = ref({})
	const router = useRouter()
	const route = useRoute()

	const getHrvData = async () => {
		try {
			const response = await api.get(
				`/api/hrv/report/${route.query?.reportId}`
			)

			if (response.status != 200) {
				throw new Error(`HTTP error! status: ${response.status}`)
			}

			const data = response.data

			if (data.code != 0) {
				throw new Error(`API error! code: ${data.code}`)
			}

			return data.data
		} catch (error) {
			console.error(
				"There was a problem with the fetch operation:",
				error
			)
			throw error
		}
	}

	const exportToPDF = () => {
		notifyPrintPdf()
	}

	const handleGoBack = () => {
		const result = notifyAndroidOnFinish()
		if (!result) {
			const params = new URLSearchParams(window.location.search)
			const urlParam = params.toString()
			router.replace("/hrvinit?" + urlParam).then(() => {
				window.location.reload()
			})
		}
	}
	const handleExit = () => {
		console.log("exit")
		goBackAppHomePage()
	}
	// 定义一个异步函数，用于初始化HRV数据
	const initHrvData = async () => {
		// 将获取到的HRV数据赋值给hrv_data
		hrv_data.value = await getHrvData()
		// 将HRV数据中的基本信息赋值给basicInfo
		basicInfo.value = hrv_data.value.basicInfo
		// 将HRV数据中的RR间隔赋值给RRIntervals
		RRIntervals.value = hrv_data.value.rrIntervals
		// 将HRV数据中的HRV分析结果赋值给hrvInfo
		hrvInfo.value = hrv_data.value.hrvAnalyse
		// 将HRV数据中的神经信息赋值给nerveInfo
		nerveInfo.value = hrv_data.value.nerve
		// 将HRV数据中的压力信息赋值给pressureInfo
		pressureInfo.value = hrv_data.value.pressure
		// 是否展示图形化参数模块
		showHrvGraphic.value = hrv_data.value.showHrvGraphic
		showHrvBottomSignature.value = hrv_data.value.showHrvBottomSignature
	}

	return {
		showHrvBottomSignature,
		showHrvGraphic,
		hrv_data,
		basicInfo,
		RRIntervals,
		hrvInfo,
		nerveInfo,
		pressureInfo,
		getHrvData,
		exportToPDF,
		handleGoBack,
		handleExit,
		initHrvData,
	}
}
