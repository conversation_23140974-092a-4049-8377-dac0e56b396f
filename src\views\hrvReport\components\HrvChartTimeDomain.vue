<template>
	<div>
		<table class="rr-chart-table">
			<thead>
				<!-- 第一行显示直方图标题 -->
				<tr>
					<th colspan="6" class="chart-header">
						{{ $t("rr_interval_histogram_key") }}
					</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<!-- 第二行绘制直方图 -->
					<td colspan="6">
						<div class="chart-container">
							<canvas id="rrHistogramChart"></canvas>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue"
import { Chart, registerables } from 'chart.js';

// 注册Chart.js组件
Chart.register(...registerables);


// 定义接收的 props
const props = defineProps({
	RRIntervals: Array, // RRInterval 是一个数组，包含 RR 间期数据
})

const rrChart = ref(null)

// 计算并过滤超过 p99 的离群数据
const filterOutliers = (data) => {
	const p99 = Math.percentile(data, 99) // 计算第 99 百分位数
	return data.filter((rr) => rr <= p99) // 过滤掉超过 p99 的离群点
}

// 计算百分位数的辅助函数
Math.percentile = function (arr, p) {
	arr.sort((a, b) => a - b) // 排序
	const index = (p / 100) * (arr.length - 1)
	const lower = Math.floor(index)
	const upper = lower + 1
	const weight = index % 1

	if (upper >= arr.length) return arr[lower]
	return arr[lower] * (1 - weight) + arr[upper] * weight
}

// 创建用于分桶处理的函数，step 为 10ms
const createBuckets = (data, step) => {
	const minRR = Math.floor(Math.min(...data) / step) * step
	const maxRR = Math.ceil(Math.max(...data) / step) * step

	// 创建区间
	const buckets = {}
	for (let i = minRR; i <= maxRR; i += step) {
		buckets[i] = 0
	}

	// 将 RR 间期数据分配到区间中
	data.forEach((rr) => {
		const bucketKey = Math.floor(rr / step) * step
		if (bucketKey in buckets) {
			buckets[bucketKey] += 1
		}
	})

	// 计算占比
	const total = data.length
	const labels = Object.keys(buckets)
	const values = Object.values(buckets).map((count) => (count / total) * 100) // 计算为百分比

	return { labels, values }
}

// 创建绘制 RR 间期分布直方图的函数
const createHistogram = () => {
	const ctx = document.getElementById("rrHistogramChart").getContext("2d")

	// 过滤超过 p99 的数据
	const filteredRR = filterOutliers(props.RRIntervals)

	// 生成分布数据，步长为 10ms
	const { labels, values } = createBuckets(filteredRR, 10)

	// 创建并显示直方图
	rrChart.value = new Chart(ctx, {
		type: "bar",
		data: {
			labels, // 横轴的 RR 取值区间
			datasets: [
				{
					label: "RR Interval Distribution (%)",
					data: values, // 对应的值占比
					backgroundColor: "#42A5F5",
				},
			],
		},
		options: {
			responsive: true,
			maintainAspectRatio: false, // 禁用默认比例，使用自定义宽高
			plugins: {
				legend: {
					display: false,
					position: "top",
				},
				tooltip: {
					callbacks: {
						label: function (tooltipItem) {
							return tooltipItem.raw.toFixed(2) + "%"
						},
					},
				},
			},
			scales: {
				x: {
					title: {
						display: true,
						text: "RR Intervals (ms)",
					},
				},
				y: {
					title: {
						display: true,
						text: "Percentage (%)",
					},
					beginAtZero: true,
				},
			},
		},
	})
}

// 在组件挂载时调用绘制函数
onMounted(() => {
	createHistogram()
})

// 监听 RRInterval 的变化并更新图表
watch(
	() => props.RRIntervals,
	() => {
		if (rrChart.value) {
			rrChart.value.destroy() // 销毁旧图表
			createHistogram() // 重新创建新图表
		}
	}
)
</script>

<style scoped>
.rr-chart-table {
	width: 100%;
	border-collapse: collapse;
}

.rr-chart-table th,
.rr-chart-table td {
	border: 1px solid #ddd;
	padding: 8px;
	text-align: center;
}

/* 设置表格标题样式 */
.chart-header {
	text-align: left !important;
	background-color: #17d7e2;
	color: white;
	font-weight: bold;
}

.chart-container {
	width: 100%;
	height: 400px; /* 确保容器高度适中 */
}

canvas {
	display: block;
	width: 100% !important; /* 确保 canvas 使用整个容器宽度 */
	height: 100% !important; /* 确保 canvas 使用整个容器高度 */
}
</style>
