<template>
	<el-dialog
		:close-on-click-modal="false"
		v-model="dialogVisible"
		width="536"
		center
		:show-close="false"
		@close="emit('onFinish')"
	>
		<template #header>
			<div class="custom-header">
				<img
					src="@/assets/hrv/testFinishClose.png"
					alt="close-icon"
					class="close-icon"
					@click="emit('onFinish')"
				/>
			</div>
		</template>
		<div class="dialog-body">
			<img
				class="finish-icon"
				src="@/assets/hrv/testFinish.png"
				alt="icon"
			/>
			<span
      class="finish-tip"
				>{{ mode==='scan'? $t("hrv_finish_tip_scan"): $t("hrv_finish_tip_input") }}</span
			>
			<div class="btn-box">
				<!-- <div @click="emit('goBack')" class="finish-btn">
					{{ $t("hrv_back_btn") }}
				</div> -->
				<div @click="emit('onFinish')" class="finish-btn">
					{{ $t("hrv_finish_btn") }}
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, defineEmits, defineProps, computed } from "vue"
const dialogVisible = computed(() => props.visible)
const emit = defineEmits(["onFinish"]) // 定义 emit 方法
const props = defineProps({
	visible: Boolean,
	mode: String,
})
</script>

<style scoped lang="scss">
.custom-header {
	display: flex;
	justify-content: flex-end;
	.close-icon {
		width: 20px;
		height: 20px;
		cursor: pointer;
	}
}
.dialog-body {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	font-weight: 500;
	font-size: 25px;
	color: #333333;
	.finish-icon {
		width: 151px;
		height: 151px;
		margin-bottom: 28px;
	}
  .finish-tip{
    // display: flex;
    // justify-content: center;
    // align-items: center;
    padding: 0 20px;
    text-align: center;
  }
	.btn-box {
		display: flex;
		justify-content: space-around;
		align-content: center;
		width: 100%;
		.finish-btn {
			width: 180px;
			height: 53px;
			background: #eb4e89;
			border-radius: 27px;
			font-weight: 500;
			font-size: 23px;
			color: #ffffff;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 58px;
			margin-bottom: 41px;
		}
	}
}
</style>
