<template>
	<div class="hrv-finished">
		<div class="hrv-finished-imagebox">
			<img
				:src="infoImage"
				class="hrv-finished-imagebox-image"
				alt="icon"
			/>
			{{ infoTip }}
		</div>
		<div class="hrv-finished-btnbox">
			<div
				v-if="processStage !== RecordStage.REPORT_COMPLETE"
				class="hrv-finished-btnbox-back"
				@click="handleGoBack"
			>
				{{ $t("hrv_finished_backbtn_key") }}
			</div>
			<div
				class="hrv-finished-btnbox-submit"
				@click="handleSubmit"
				v-if="
					processStage !== RecordStage.USER_EXPIRE &&
					processStage !== RecordStage.FRONTEND_ERROR
				"
			>
				{{ submitText }}
			</div>
		</div>
		<div class="hrv-finished-logo">
			<img :src="airdocLogo" alt="logo" class="hrv-finished-logo-image" />
			<span class="baseline">|</span>
			<span>{{ $t("hrv_finished_title_key") }}</span>
		</div>
		<baseModal
			:visible="isShowTipModal"
			:content="$t('hrv_finished_quit_test_key')"
			:showClose="false"
			@onFinish="handleModalSubmit"
			@onCancel="handleModalGoBack"
		/>
	</div>
</template>

<script setup>
import { computed, onMounted, ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import { RecordStage } from "@/constants/recordStage"
import dataError from "@/assets/hrv/hrvfinished/hrvfinished_data_error.png"
import networkError from "@/assets/hrv/hrvfinished/hrvfinished_network_error.png"
import success from "@/assets/hrv/hrvfinished/hrvfinished_success.png"
import logoEn from "@/assets/hrv/airdoc_logo_en.png"
import logoZh from "@/assets/hrv/airdoc_logo_zh.png"
import { goBackAppHomePage } from "@/utils/android.js"
import baseModal from "@/components/hrv/baseModal.vue"
import { useI18n } from "vue-i18n"
import { getCurrentLanguage } from "@/utils/i18n"
import { useHrvStore } from "@/stores/hrvStore.js"

const hrvStore = useHrvStore()
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

const isShowTipModal = ref(false)
const detectMode = ref("")
const processStage = ref("")
const accessToken = ref("")
const infoImage = computed(() => {
	if (processStage.value == RecordStage.REPORT_COMPLETE) {
		return success
	} else if (processStage.value == RecordStage.NETWORK_ERROR) {
		return networkError
	} else if (processStage.value == RecordStage.SUBMITTING_DATA_ERROR) {
		return dataError
	} else {
		return dataError
	}
})
const infoTip = computed(() => {
	if (processStage.value == RecordStage.REPORT_COMPLETE) {
		if (detectMode.value == "scan") {
			return t("hrv_finish_tip_scan")
		} else {
			return t("hrv_finish_tip_input")
		}
	} else if (processStage.value == RecordStage.NETWORK_ERROR) {
		return t("hrv_finished_network_error_key")
	} else if (processStage.value == RecordStage.SUBMITTING_DATA_ERROR) {
		return t("hrv_finished_submit_error_key")
	} else if (processStage.value == RecordStage.USER_EXPIRE) {
		return t("hrv_test_hook_without_token_key")
	} else {
		return t("hrv_finished_unknown_error_key")
	}
})
const airdocLogo = computed(() => {
	if (getCurrentLanguage() == "zh-CN") {
		return logoZh
	} else {
		return logoEn
	}
})
const submitText = computed(() => {
	console.log("🚀 ~ submitText ~ processStage.value:", processStage.value)
	if (processStage.value == RecordStage.REPORT_COMPLETE) {
		return t("hrv_finish_btn")
	} else if (processStage.value == RecordStage.NETWORK_ERROR) {
		return t("hrv_finished_reupload_key")
	} else if (processStage.value == RecordStage.SUBMITTING_DATA_ERROR) {
		return t("hrv_finished_redetect_key")
	} else {
		return t("hrv_finish_btn")
	}
})
const handleGoBack = () => {
	isShowTipModal.value = true
}
const handleSubmit = () => {
	if (processStage.value == RecordStage.REPORT_COMPLETE) {
		onFinish()
	} else if (processStage.value == RecordStage.NETWORK_ERROR) {
		reuploadDataInfo()
	} else if (processStage.value == RecordStage.SUBMITTING_DATA_ERROR) {
		redetect()
	} else {
		return onFinish()
	}
}
const handleModalGoBack = () => {
	isShowTipModal.value = false
}
const handleModalSubmit = () => {
	isShowTipModal.value = false
	onFinish()
}
const reuploadDataInfo = async () => {
	hrvStore.sendDataToBackend(router, route)
}
const redetect = () => {
	const url = router.resolve({
		path: "/hrvtest",
		query: {
			...route.query,
		},
	}).href

	window.location.replace(url) // 页面跳转并强制刷新
}
const onFinish = () => {
	// 返回到app首页
	goBackAppHomePage()
}
onMounted(() => {
	detectMode.value = route.query.detectMode || ""
	processStage.value = Number(route.query.process) || ""
	accessToken.value = route.query.accessToken || ""
	if (accessToken.value) {
		window.localStorage.setItem("user_token", accessToken.value)
	}
})
</script>

<style lang="scss" scoped>
.hrv-finished {
	width: 100vw;
	height: 100vh;
	background-size: 100% 100%;
	background: url("https://img3.airdoc.com/staticResources/data/static/js/hrvinit_back.png");
	font-weight: 500;
	font-size: 1.6rem;
	color: #333333;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	font-family: PingFang SC, PingFang SC;
	&-imagebox {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-bottom: 5rem;
		&-image {
			width: 28.44rem;
			object-fit: cover;
			margin-bottom: 1.25rem;
		}
		font-size: 2.25rem;
		color: #666666;
	}
	&-btnbox {
		font-weight: 500;
		font-size: 3rem;
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 50%;
		margin-bottom: 8.94rem;
		&-back {
			width: 24.75rem;
			height: 6.38rem;
			border-radius: 5rem 5rem 5rem 5rem;
			border: 0.06rem solid #eb4e89;

			color: #eb4e89;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		&-submit {
			width: 24.75rem;
			height: 6.38rem;
			background: #eb4e89;
			border-radius: 5rem 5rem 5rem 5rem;

			color: #ffffff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	&-logo {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-weight: 500;
		font-size: 1.63rem;
		color: #d1035a;
		letter-spacing: 1px;
		line-height: 2.3rem;
		.baseline {
			font-size: 1.2rem;
			margin: 0 1.25rem;
		}
		&-image {
			height: 2.69rem;
			object-fit: cover;
		}
	}
}
</style>
