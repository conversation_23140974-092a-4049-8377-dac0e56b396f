module.exports = {
	plugins: {},
	chainWebpack: (config) => {
		config.module
			.rule("wasm")
			.test(/\.wasm$/)
			.use("file-loader")
			.loader("file-loader")
			.end()
	},
	configureWebpack: {
		rollupOptions: {
			external: ["wavesurfer.js"], // 添加需要外部化的模块
		},
	},
	devServer: {
		headers: {
			"Access-Control-Allow-Origin": "*",
			"Content-Type": "application/wasm", // 设置正确的 MIME 类型
		},
	},
}
