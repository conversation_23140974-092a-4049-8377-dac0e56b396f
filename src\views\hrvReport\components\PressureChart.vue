<template>
	<div class="pressure-chart">
		<div class="chart-title">{{ title }}</div>
		<div class="chart-content">
			<div class="chart-left">
				<span
					:style="{ color: getColorForValue() }"
					class="chart-value"
					>{{ value }}</span
				>
			</div>
			<div class="chart-right">
				<!-- The triangular pointer positioned on the left of the chart-bar -->
				<div
					class="chart-pointer"
					:style="{ left: `${calculatePointerPosition()}%` }"
				></div>
				<div class="chart-bar">
					<div :class="getChartGradientClass()"></div>
				</div>
			</div>
			<div class="chart-description">{{ description }}</div>
			<div class="chart-description">
				{{ compareDescription }}
			</div>
		</div>
	</div>
</template>

<script setup>
import { computed } from "vue"
import { useI18n } from "vue-i18n"

// Initialize i18n
const { t } = useI18n()

// Props for title, value, and bar range
const props = defineProps({
	index: String,
	title: String,
	value: Number,
	barMax: Number,
	barMin: Number,
	barValue: Number,
	description: String,
  compareDescription:String
})

// Calculate the position of the pointer on the bar
const calculatePointerPosition = () => {
	const range = props.barMax - props.barMin
	const pointerWidth = 7.5; // 假设指针宽度为7.5px，需要根据实际CSS定义调整
	const offset = (pointerWidth / 2) / (props.barMax - props.barMin) * 100; // 将像素宽度转换为百分比偏移量
	let position = ((props.barValue - props.barMin) / range) * 100 - offset;

	// 确保指针位置在合理范围内，避免超出左右边界
	if (position<= 0) {
		position = -5;
	} else if (position > 100 - pointerWidth) { // 100 - pointerWidth 是为了确保指针完全在条形图内
		position = 100 - pointerWidth;
	}

	return position;
}

// Map the value to a color based on the bar range
const getColorForValue = () => {
	// if (props.barValue <= 30) return '#008000'; // Green for values ≤ 30
	// else if (props.barValue <= 60) return '#FFA500'; // Orange for values between 31 and 60
	// else return '#FF0000'; // Red for values above 60
	return "#17d7e2"
}

const getChartGradientClass = () => {
	console.log(props.index)
	if (props.index == "stressResistance") {
		return "chart-gradient-resistance"
	} else if (props.index == "balance") {
		return "chart-gradient-balance"
	} else if (props.index == "activity") {
		return "chart-gradient-activity"
	} else {
		return "chart-gradient"
	}
}
const getDescriptionText = () => {
	const { index, value } = props
	let num = 0

	switch (index) {
		case "stressResistance":
			// 修正取模运算为减法
			num = Math.round((100 - value) * 0.1 + value)
			return num ? t("exceed_percentage_key", { num }) : ""

		case "mentalPressure":
			num = Math.round(value * 0.1 + (100 - value / 2))
			return num ? t("better_than_percentage_key", { num }) : ""

		case "physicalPressure":
			num = Math.round(value * 0.1 + (100 - value / 2))
			return num ? t("better_than_percentage_key", { num }) : ""

		case "balance":
			// 修正取模运算为减法，并修正计算逻辑
			num = Math.round(100 - (value * 0.1 + Math.abs(value - 50)))
			return num ? t("better_than_percentage_key", { num }) : ""

		case "activity":
			num = Math.round(50 + value / 2)
			return num ? t("better_than_percentage_key", { num }) : ""

		default:
			return ""
	}
}
</script>

<style scoped>
.pressure-chart {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	width: 100%;
}

.chart-title {
	font-size: 14px;
	font-weight: bold;
	margin-bottom: 10px;
}

.chart-content {
	display: flex;
	justify-content: flex-start;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.chart-left {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	flex-grow: 0.5;
	/* padding-right: 10px; */
	/* margin-right: 30px; */
}

.chart-value {
	font-size: 48px; /* Large font size */
	font-weight: bold;
}

.chart-right {
	position: relative;
	width: 120px;
	height: 20px;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	/* margin-right: 20px; Add right margin to make the chart more compact */
	/* transform: rotate(90deg); */
}

.chart-bar {
	position: relative;
	width: 100%;
	height: 100%;
	border: 1px solid #000;
}

.chart-gradient {
	width: 100%;
	height: 100%;
	background-color: #00ff00;
	background: linear-gradient(
		to right,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#a0ff00,
		#f0ff00,
		#fff500,
		#ff0000
	); /* Gradient from green to red */
}

.chart-gradient-resistance {
	width: 100%;
	height: 100%;
	background-color: #00ff00;
	background: linear-gradient(
		to left,
		#00ff00,
		#00ff00,
		#00ff00,
		#a0ff00,
		#f0ff00,
		#fff500,
		#ff0000
	);
}

.chart-gradient-balance {
	width: 100%;
	height: 100%;
	background-color: #00ff00;
	background: linear-gradient(
		to left,
		#ff0000,
		#ffff00,
		#ffff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#ffff00,
		#ffff00,
		#ff0000
	);
}

.chart-gradient-activity {
	width: 100%;
	height: 100%;
	background-color: #00ff00;
	background: linear-gradient(
		to right,
		#ff0000,
		#ffa000,
		#ffff00,
		#ffff00,
		#ffff00,
		#ffff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#00ff00,
		#ff0000
	);
}

/* The triangular pointer */
.chart-pointer {
	position: absolute;
	left: -15px; /* Move the triangle closer to the chart-bar */
	width: 0;
	height: 0;
	border-left: 15px solid black; /* Creates the right-facing triangle */
	border-top: 7.5px solid transparent;
	border-bottom: 7.5px solid transparent;
	top: -15px;
	transform: rotate(90deg);
}

.chart-description {
	margin-top: 10px;
	font-size: 12px;
	text-align: left;
}
</style>
