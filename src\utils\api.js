import { api } from "@/api"

export async function sendJsonData(requestInfo) {
	const request_body = requestInfo


	try {
		const response = await api.post(
			"/api/hrv/rppg",
			request_body,
		)

		if (response.status != 200) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const data = await response.data
		console.log("Response received:", data)

		if (data.code != 0) {
			throw new Error(`API error! code: ${data.code}`)
		}

		return data
	} catch (error) {
		console.error("There was a problem with the fetch operation:", error)
		throw error
	}
}

const performSpeedTest = async (url, expectedFileSize) => {
	try {
		const downloadStartTime = performance.now()
		const response = await fetch(url) // 直接请求完整地址

		if (!response.ok) { // 更推荐使用 response.ok 来检查成功状态 (2xx)
			throw new Error(
				`Server Error: ${response.status} ${response.statusText}`
			)
		}

		// **关键修改：将响应体完全读取为 Blob**
		const blob = await response.blob(); // <--- 这里是重点！

		const downloadEndTime = performance.now()

		let actualFileSizeBytes = expectedFileSize;
		const contentLength = response.headers.get("content-length"); // 使用 get 方法获取头部
		if (contentLength) {
			actualFileSizeBytes = parseInt(contentLength, 10);
		} else {
			console.warn(
				`Content-Length header not found. Using predefined file size for speed calculation, results may be inaccurate.`
			);
		}


		if (blob.size === 0 && actualFileSizeBytes > 0) {
			console.warn(
				`Downloaded blob size is 0, but Content-Length > 0. Speed calculation may be incorrect or download silently failed.`
			);
		} else if (blob.size > 0) {
			console.log("🚀 ~ performSpeedTest ~ blob.size:", blob.size);
			actualFileSizeBytes = blob.size; // 优先使用实际下载的 blob.size
		}

		const durationSeconds = (downloadEndTime - downloadStartTime) / 1000;

		if (durationSeconds > 0 && actualFileSizeBytes > 0) {
			const bits = actualFileSizeBytes * 8;
			const speedKbps = (bits / durationSeconds / 1_000).toFixed(2); // Convert to kbps
			return parseFloat(speedKbps);
		} else {
			console.error(
				"Speed test calculation error: duration or file size is zero."
			);
			return 0;
		}
	} catch (e) {
		console.error("Network speed test failed:", e);
		return 0; // Indicate failure
	}
}
export {
  performSpeedTest
}