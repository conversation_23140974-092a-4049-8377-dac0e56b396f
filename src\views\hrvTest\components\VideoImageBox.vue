<template>
	<div
		class="hrvInit-content-imagebox"
		v-if="stage === RecordStage.IMAGE_DISPLAY"
	>
  <!-- <div class="scatter-plot">
    <ScatterPlot :chartData="currentImage"/>
  </div> -->
		<img
			v-if="stage === RecordStage.IMAGE_DISPLAY && currentImage"
			class="image-display"
			:src="currentImage.materialPath"
		/>
		<!-- :style="{ left: currentImage.x + '%', top: currentImage.y + '%' }" -->
	</div>
</template>

<script setup>
import { defineProps, onMounted } from "vue"
import { RecordStage } from "@/constants/recordStage"
import ScatterPlot from "./ScatterPlot.vue"
const props = defineProps({
	currentImage: {
		type: Object,
		required: true,
	},
	stage: {
		type: Number,
		required: true,
	},
})
onMounted(() => {
	console.log(props, "props")
})
</script>

<style scoped lang="scss">
.hrvInit-content-imagebox {
	flex: 1;
	height: 100vh;
	position: relative;
	background: black;
  .scatter-plot{
    width: 17rem;
    height: 17rem;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
  }
	.image-display {
		height: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
}
</style>
