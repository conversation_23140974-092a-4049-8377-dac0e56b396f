<template>
  <div class="container">
    <div class="header">
      <h1>GenA<PERSON>心理健康助手<span class="version">（{{ version }}）</span></h1>
    </div>
    <div class="main-container">
      <div class="video-section">
        <video ref="video" autoplay class="mirror"></video>
        <canvas ref="canvas" class="facemesh"></canvas>
        <div v-show="showMetrics" ref="metricsSection" class="metrics-section" @mousedown="startDrag">
          <div class="metrics">
            <p>心率: {{ heartRate }} bpm</p>
            <p>MeanRR: {{ meanRR }} ms</p>
            <p>SDNN: {{ sdnn }} ms</p>
            <p>RMSSD: {{ rmssd }} ms</p>
            <p>pNN50：{{ pnn50}}%</p>
            <p>LF：{{lf}} ms&sup2</p>
            <p>HF：{{hf}} ms&sup2</p>
            <p>LF/HF：{{lf_hf_ratio}}</p>
            <p>FPS：{{fps}}</p>
            <div ref="chart" style="height: 100px;"></div>
          </div>
          <div class="additional-metrics">
            <p>凝视点（NEF）：25</p>
            <p>反应探索评分1（RSS1）：7</p>
            <p>反应探索评分2（RSS2）：7</p>
          </div>
        </div>
      </div>
      <div class="record-section">
        <div class="input-section">
          <div class="input-container">
            <p>对话记录：</p>
            <el-input type="textarea" v-model="transcription" class="custom-input"
                      :rows="rows" :disabled="isGeneratingSummary"></el-input>
          </div>
          <div class="input-container" style="margin-top: 20px;">
            <p>诊断摘要：</p>
            <div v-html="renderedSummary" class="summary-container"
                 :disabled="isGeneratingSummary || recording"></div>
          </div>
        </div>
        <div style="margin-top: 20px; display: flex; align-items: center; justify-content: space-between;">
          <div class="checkbox-group">
            <el-checkbox v-model="showGrid">显示面部网格</el-checkbox>
            <el-checkbox v-model="showPupil">显示瞳孔轮廓</el-checkbox>
            <el-checkbox v-model="showMetrics">显示生理指标</el-checkbox>
          </div>
          <el-button type="primary" @click="handleRecording" :disabled="isGeneratingSummary">
            {{ recording ? '生成摘要' : '开始记录' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
import {ref, watch, onMounted, onBeforeUnmount, nextTick} from 'vue';
import * as echarts from 'echarts';
import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import axios from 'axios';
import {marked} from 'marked';
import {createFaceLandmarker, initOpenCV} from '../utils/vision-utils.js';
import {DrawingUtils, FaceLandmarker} from '@mediapipe/tasks-vision';
import {saveToCSV} from '@/utils/base-utils.js';
import DigitalFilter from '@/utils/DigitalFilter.js';
import HRCalculator from '@/utils/HRCalculator.js';

let speechRecognizer = null;

const version = ref('v0.1');

const heartRate = ref(0);
const meanRR = ref(0);
const sdnn = ref(0);
const rmssd = ref(0);
const pnn50 = ref(0);

const lf = ref(0);
const hf = ref(0);
const lf_hf_ratio = ref(0);

const fps = ref(0);

const recording = ref(false);
const isGeneratingSummary = ref(false);
const transcription = ref('');
const rows = ref(10);
const summary = ref('');
const renderedSummary = ref('');
const showGrid = ref(false);
const showPupil = ref(false);
const showMetrics = ref(true);

const video = ref(null);
const canvas = ref(null);
const chart = ref(null);
let myChart = null;

const metricsSection = ref(null);
let isDragging = false;
let startX, startY, initialLeft, initialTop;

let faceLandmarker = null;
let mediaPipeInitialized = false;

let frameBuffer = [];
let frameCount = 0;
const bufferSize = 10;

let rmean = 0;
let gmean = 0;
let bmean = 0;
let rgb_means = [];
let time_diffs = [];
let pos_results = [];
let filter_results = [];
let last_process_time = 0;
let total_duration_ms = 0;
const window_size = 10;
const max_duration_ms = 300000; // 300,000ms = 5min
const save_data_for_debug = false;

let digital_filter = null;
let hr_calculator = null;

const hrvChartOptions = {
  xAxis: {
    type: 'time',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [{
    data: [],
    type: 'line',
    symbol: 'none',
    showSymbol: false,
    // progressive: 1000,
    // progressiveThreshold: 2000,
  }],
};
const lower_face = [200, 431, 411, 340, 349, 120, 111, 187, 211];

const handleRecording = () => {
  if (!recording.value) {
    startRecording();
  } else {
    stopRecognition();
  }
};

const startRecording = async () => {
  recording.value = true;

  const speechData = await get_speech_token();
  const token = speechData.token;
  const region = speechData.region;
  const speechConfig = SpeechSDK.SpeechConfig.fromAuthorizationToken(token, region);
  speechConfig.speechRecognitionLanguage = 'zh-CN';
  speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_SingleLanguageIdPriority, 'SpeakerDiarization');
  speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_SpeakerDiarizationEnabled, 'true');

  const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
  speechRecognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

  transcription.value = '';
  summary.value = '';
  speechRecognizer.recognized = (s, e) => {
    if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
      transcription.value += e.result.text + '\n';  // 确保最终结果按行添加并换行
    }
  };

  speechRecognizer.startContinuousRecognitionAsync();
};

const get_speech_token = async () => {
  try {
    const authorizationEndpoint = "https://grace.ybbywb.com:8082/api/v1/get-speech-token";
    const res = await axios.get(authorizationEndpoint);
    const token = res.data.token;
    const region = res.data.region;
    console.log('Token fetched from back-end: ' + token);
    return { token, region };
  } catch (err) {
    console.log(err);
  }
}

const stopRecognition = async () => {
  recording.value = false;
  if (speechRecognizer) {
    speechRecognizer.stopContinuousRecognitionAsync();
    speechRecognizer = null;
  }
  isGeneratingSummary.value = true;
  await generateSummary();
  isGeneratingSummary.value = false;
};

const generateSummary = async () => {
  try {
    const response = await axios.post('https://grace.ybbywb.com:8082/chat/all_messages', {
      messages: transcription.value  // 将 transcription 的内容作为 message 传递
    }, {
      timeout: 60000
    });

    console.log('Response:', response);
    // 将返回的摘要存储到 summary 中
    summary.value = response.data.message || '摘要生成失败';
    renderedSummary.value = marked(summary.value);
  } catch (error) {
    console.error('生成摘要时出错:', error);
    summary.value = '生成摘要时发生错误';
  }
};

const setupCamera = () => {
  navigator.mediaDevices.getUserMedia({ video: true })
  .then(stream => {
    video.value.srcObject = stream;
    video.value.addEventListener("loadeddata", () => {
      predictWebcam()
    });
  })
  .catch(error => {
    console.error("摄像头访问失败：", error);
  });
};

const extractROI = (canvas, landmarks) =>  {
  const canvas_obj = canvas.value;

  const points = lower_face.map(index => ({
    x: Math.round(landmarks[index].x * canvas_obj.width),
    y: Math.round(landmarks[index].y * canvas_obj.height),
  }));

  const ctx = canvas.value.getContext('2d');
  const imageData = ctx.getImageData(0, 0, canvas_obj.width, canvas_obj.height);

  // 使用OpenCV处理ROI
  let dst = new cv.Mat();
  let src = cv.matFromImageData(imageData);
  let mask = new cv.Mat(src.rows, src.cols, cv.CV_8UC1, new cv.Scalar(0));

  // 创建ROI mask
  let roiPoints = new cv.MatVector();
  roiPoints.push_back(cv.matFromArray(points.length, 1, cv.CV_32SC2, points.flatMap(p => [p.x, p.y])));
  cv.fillPoly(mask, roiPoints, new cv.Scalar(255, 255, 255));

  // 应用mask
  src.copyTo(dst, mask);

  // 计算ROI区域的均值
  let mean = cv.mean(dst, mask);

  // 保存均值到this.rgb_means
  rgb_means.push(mean);

  // 清理资源
  roiPoints.delete();
  mask.delete();
  src.delete();
  return dst;
}

const movingAverageUpdate = (xs, winsize) => {
  if (xs.length === 0) {
    return NaN;
  }
  return xs.slice(-winsize).reduce((a, b) => a + b, 0) / winsize;
}

const nanmean = (arr) => {
  const filtered = arr.filter(value => !isNaN(value));
  return filtered.reduce((a, b) => a + b, 0) / filtered.length;
}

const nanstd = (arr) => {
  const mean = nanmean(arr);
  const squaredDiffs = arr.map(value => (value - mean) ** 2);
  const avgSquaredDiff = nanmean(squaredDiffs);
  return Math.sqrt(avgSquaredDiff);
}

const pos_calculate = () => {
  if (rgb_means.length >= window_size) {
    const rs = rgb_means.map(row => row[0]);
    const gs = rgb_means.map(row => row[1]);
    const bs = rgb_means.map(row => row[2]);

    rmean = movingAverageUpdate(rs, window_size);
    gmean = movingAverageUpdate(gs, window_size);
    bmean = movingAverageUpdate(bs, window_size);

    // temporal normalization
    const rn = rs.slice(-window_size).map(r => r / (rmean || 1));
    const gn = gs.slice(-window_size).map(g => g / (gmean || 1));
    const bn = bs.slice(-window_size).map(b => b / (bmean || 1));

    // projection
    const s1 = gn.map((g, i) => g - bn[i]);
    const s2 = gn.map((g, i) => -2 * rn[i] + g + bn[i]);

    // tuning
    const s1std = nanstd(s1);
    const s2std = nanstd(s2);
    const h = s1.map((s1i, i) => s1i + (s1std / s2std) * s2[i]);
    pos_results.push(0);
    for (let i = 0; i < window_size; i++) {
      pos_results[pos_results.length - window_size + i] += (h[i] - nanmean(h));
    }
    return pos_results[pos_results.length - window_size];
  }

  pos_results.push(0);
  return 0;
}

const processFrame = (canvas, landmarks) =>  {
  // 将ROI添加到缓冲区
  let roi = extractROI(canvas, landmarks);
  frameBuffer.push(roi);
  if (frameBuffer.length > bufferSize) {
    let oldFrame = frameBuffer.shift();
    if (oldFrame instanceof cv.Mat) {
      oldFrame.delete();
    }
  }

  const proc_time = performance.now();

  let time_diff = 0
  if (last_process_time > 0) {
    time_diff = proc_time - last_process_time;
  }
  time_diffs.push(time_diff)
  last_process_time = proc_time;
  total_duration_ms += time_diff;
  frameCount++;

  if (frameCount % 30 === 0) {
    let sample_count = 30;
    let last10TimeDiffs = time_diffs.slice(-sample_count);
    let sum = last10TimeDiffs.reduce((acc, val) => acc + val, 0);
    let average = sum / sample_count;
    fps.value = (1000/average).toFixed(2);
  }

  const pos_result = pos_calculate();
  const filtered_result = digital_filter.process(pos_result);
  filter_results.push(filtered_result);

  const result = hr_calculator.update(time_diffs, filter_results);
  heartRate.value = result.bpm.toFixed(2);

  if (result.timeDomainHRV) {
    meanRR.value = result.timeDomainHRV.meanRR.toFixed(2);
    sdnn.value = result.timeDomainHRV.sdnn.toFixed(2);
    rmssd.value = result.timeDomainHRV.rmssd.toFixed(2);
    pnn50.value = result.timeDomainHRV.pnn50.toFixed(2);
  }

  if (result.frequencyDomainHRV) {
    lf.value = result.frequencyDomainHRV.lf.toFixed(2);
    hf.value = result.frequencyDomainHRV.hf.toFixed(2);
    lf_hf_ratio.value = result.frequencyDomainHRV.lf_hf_ratio.toFixed(2);
  }

  if (save_data_for_debug && time_diffs.length % 1000 == 0) {
    const headers = ["Time_Diffs", "R_Means", "G_Means", "B_Means", "A_Means", "Pos_Results", "Filter_Results"];
    const data = rgb_means.map((_, i) => [time_diffs[i], rgb_means[i], pos_results[i], filter_results[i]]);

    // 调用通用函数保存 CSV
    saveToCSV(headers, data, "hrv");
  }


  while (total_duration_ms > max_duration_ms) {
    total_duration_ms -= time_diffs[0];

    time_diffs.shift();
    rgb_means.shift();
    pos_results.shift();
    filter_results.shift();
  }
}

const predictWebcam = async () => {

  let a = [1.000000, -3.482555,  4.623822, -2.781063, 0.641352];
  let b = [0.020083,  0.000000, -0.040167,  0.000000, 0.020083];
  digital_filter = new DigitalFilter(b, a);
  hr_calculator = new HRCalculator(30, 300);

  const predict = async () => {
    if (!mediaPipeInitialized) {
      return;
    }

    if (!canvas.value) {
      return;
    }

    const canvasCtx = canvas.value.getContext('2d');
    const canvasWidth = canvas.value.width;
    const canvasHeight = canvas.value.height;

    canvas.value.width = video.value.videoWidth;
    canvas.value.height = video.value.videoHeight;

    const drawingUtils = new DrawingUtils(canvasCtx);
    const results = await faceLandmarker.detectForVideo(video.value, performance.now());

    canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);
    canvasCtx.drawImage(video.value, 0, 0, canvasWidth, canvasHeight);

    if (results.faceLandmarks) {
      for (const landmarks of results.faceLandmarks) {

        processFrame(canvas, landmarks);

        if (showPupil.value){
          drawingUtils.drawConnectors(
              landmarks,
              FaceLandmarker.FACEMESH_IRISES, // 使用 FACEMESH_IRISES 绘制瞳孔
              { color: "#FF3030", lineWidth: 1 }
          );
        }

        if (showGrid.value) {
          // Draw connectors and dots as before
          drawingUtils.drawConnectors(
              landmarks,
              FaceLandmarker.FACE_LANDMARKS_TESSELATION,
              { color: "#C0C0C070", lineWidth: 1 }
          );

          // Draw red circles on lower face points
          canvasCtx.fillStyle = 'red';
          const lowerFacePoints = [];
          for (const index of lower_face) {
            const x = landmarks[index].x * canvasWidth;
            const y = landmarks[index].y * canvasHeight;
            lowerFacePoints.push({ x, y });
            canvasCtx.beginPath();
            canvasCtx.arc(x, y, 5, 0, 2 * Math.PI); // Draw red dot with radius 5
            canvasCtx.fill();
          }

          // Draw ROI
          canvasCtx.strokeStyle = 'red';
          canvasCtx.lineWidth = 2;
          canvasCtx.beginPath();
          for (let i = 0; i < lowerFacePoints.length; i++) {
            const { x, y } = lowerFacePoints[i];
            if (i === 0) {
              canvasCtx.moveTo(x, y);
            } else {
              canvasCtx.lineTo(x, y);
            }
          }

          // Close the path to form a complete loop
          const firstPoint = lowerFacePoints[0];
          canvasCtx.lineTo(firstPoint.x, firstPoint.y);
          canvasCtx.stroke();
        }

        if (showMetrics.value && frameCount % 10 == 0) {
          updateChart();
        }
      }
    }
  }
  setInterval(predict, 1000 / 30);
}

const initChart = () => {
  if (chart.value) {
    myChart = echarts.init(chart.value);
    myChart.setOption(hrvChartOptions);
  }
};

const getCumulativeTimeDiffs = (time_diffs) => {
  let cumulative = [];
  let sum = 0;

  for (let i = 0; i < time_diffs.length; i++) {
    sum += time_diffs[i];
    cumulative.push(sum);
  }

  return cumulative;
};

const updateChart = () => {
  if (myChart) {
    const sample_count = 600;

    const cumulativeTimeDiffs = getCumulativeTimeDiffs(time_diffs.slice(-sample_count));
    const values = filter_results.slice(-sample_count);
    // console.log('cumulativeTimeDiffs length:', cumulativeTimeDiffs.length);
    // console.log('values length:', values.length);
    myChart.setOption({
      xAxis: {
        data: cumulativeTimeDiffs,
      },
      series: [{
        data: values,
      }],
    });
  }
};

const calculateRows = () => {
  const inputSection = document.querySelector('.input-section');
  if (inputSection) {
    const containerHeight = inputSection.clientHeight;


    const labelHeight = 20; // 假设 <p> 标签的高度为 20px
    const rowHeight = 23; // 每行的高度，假设为 24px
    const marginBetween = 20; // "margin-top: 20px;" 的高度
    const availableHeight = containerHeight - (labelHeight * 2) - marginBetween;
    rows.value = Math.floor(availableHeight / (2 * rowHeight));
    console.log('containerHeight:', containerHeight, "rows:", rows.value);
  }
};

// 开始拖动
const startDrag = (event) => {
  console.log('start dragging');
  isDragging = true;
  startX = event.clientX;
  startY = event.clientY;
  initialLeft = metricsSection.value.offsetLeft;
  initialTop = metricsSection.value.offsetTop;
  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', stopDrag);
};

// 拖动时
const drag = (event) => {
  if (!isDragging) return;

  const deltaX = event.clientX - startX;
  const deltaY = event.clientY - startY;

  let newLeft = initialLeft + deltaX;
  let newTop = initialTop + deltaY;

  // 获取父容器的宽度和高度
  const parentWidth = metricsSection.value.parentElement.clientWidth;
  const parentHeight = metricsSection.value.parentElement.clientHeight;

  // 确保拖动不会超出父容器边界
  const maxLeft = parentWidth - metricsSection.value.clientWidth;
  const maxTop = parentHeight - metricsSection.value.clientHeight;

  if (newLeft < 0) newLeft = 0;
  if (newLeft > maxLeft) newLeft = maxLeft;
  if (newTop < 0) newTop = 0;
  if (newTop > maxTop) newTop = maxTop;

  metricsSection.value.style.left = `${newLeft}px`;
  metricsSection.value.style.top = `${newTop}px`;
};

// 停止拖动
const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', drag);
  document.removeEventListener('mouseup', stopDrag);
  console.log('stop dragging');
};

const loadVersion = async () => {
  try {
    const response = await fetch('version.json');
    if (response.ok) {
      const data = await response.json();
      version.value = data.version; // 更新版本号
    } else {
      console.error('无法加载版本号:', response.statusText);
    }
  } catch (error) {
    console.error('获取版本号时出错:', error);
  }
};


onMounted(async () => {
  loadVersion();
  await nextTick(); // 确保DOM已完全渲染
  calculateRows();
  window.addEventListener('resize', calculateRows);
  if (showMetrics.value) {
    initChart();
  }

  try {
    console.log('create faceLandmarker...');
    faceLandmarker = await createFaceLandmarker();
    console.log('faceLandmarker initialized successfully');

    console.log('initOpenCV ...');
    await initOpenCV();
    console.log('initOpenCV successfully');

    mediaPipeInitialized = true;
  } catch (error) {
    console.error('Error initializing vision tasks:', error);
  }

  setupCamera();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateRows);
});
</script>

<style scoped>

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  padding: 10px;
  box-sizing: border-box;
  justify-content: space-between;
  background-color: #1e90ff;
}

.header {
  text-align: center;
  background: linear-gradient(to right, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.1));
  color: white;
  padding: 15px 0;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header h1 {
  font-family: 'STKaiti';
  font-weight: bold;
  font-size: 2rem;
  margin: 0;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* 添加文本阴影 */
}

.header h1 .version {
  font-size: 1.2rem; /* 版本号相对小一些 */
  color: rgba(255, 255, 255, 0.7); /* 设置稍微透明的白色，柔和一点 */
  font-style: italic; /* 版本号使用斜体 */
  margin-left: 10px; /* 标题和版本号之间的间距 */
  vertical-align: middle; /* 垂直对齐到 h1 中间 */
}

.main-container {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  justify-content: space-between;

}

.video-section {
  position: relative;
  flex: 0 0 60%;
  box-sizing: border-box;
}

.metrics-section {
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 10px;
  top: 10px;
  right: 0;
  width: 280px;
  color: #f5f5f5;
  padding: 10px;
  box-sizing: border-box;
  background: linear-gradient(to right, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.1));
  border-radius: 15px;
}

.record-section {
  flex: 0 0 40%;
  padding: 10px;
  box-sizing: border-box;
  background: linear-gradient(to right, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.1));
  border-radius: 15px;
  margin-left: 10px;
}

.video-section video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
  overflow: hidden;
}

.mirror {
  transform: scaleX(-1);
}

.facemesh {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(255, 0, 0, 0.0);
  transform: scaleX(-1);
  border-radius: 15px;
  overflow: hidden;
}


.additional-metrics {
  margin-top: 5px;
}

.record-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #e8e8e8;
}

.input-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.input-container p {
  margin-bottom: 8px;
}

.custom-input {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.custom-input {
  flex: 1;
  resize: none;
}

.summary-container {
  flex: 1;
  height: 100%; /* 初始高度和对话记录一致 */
  background-color: #f9f9f9;
  border-radius: 5px;
  padding: 10px;
  overflow: auto; /* 允许内容超出时显示滚动条 */
  box-sizing: border-box;
  white-space: pre-wrap; /* 适应 Markdown 中的换行 */
  max-height: 200px; /* 控制最大高度 */
}

.checkbox-group {
  display: flex;
  gap: 3px; /* 为复选框之间添加一些间距 */
}

</style>
