// src/stores/emotionStore.js
import { defineStore } from 'pinia'

export const useEmotionStore = defineStore('emotion', {
  state: () => ({
    analysisResults: [],
    imageLoadStatus: {},
  }),
  getters: {
    hasResults: (state) => state.analysisResults.length > 0,
  },
  actions: {
    setAnalysisResults(results) {
      this.analysisResults = results
    },
    setImageLoaded(id, type) {
      if (this.imageLoadStatus[id]) {
        this.imageLoadStatus[id][type] = true
      }
    },
    resetState() {
      this.analysisResults = [];
      this.imageLoadStatus = {};
    }
  },
})