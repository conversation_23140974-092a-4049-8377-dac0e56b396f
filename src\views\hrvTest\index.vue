<template>
	<div class="hrvInit">
		<div
			class="hrvInit-generateing"
			v-if="stage === RecordStage.REPORT_GENERATING"
		>
			<img
				src="@/assets/hrv/hrvfinished/hrvfinished_generate.gif"
				alt="gif"
			/>
			<span>
				{{ $t("hrv_generate_title_key") }}
			</span>
		</div>
		<template v-else>
			<div
				class="hrvInit-title"
				v-if="stage !== RecordStage.IMAGE_DISPLAY"
			>
				{{ $t("hrv_title_key") }}
			</div>
			<div class="hrvInit-content">
				<LoadingItems :stage="stage" :list="loadingItems" />
				<VideoImageBox :currentImage="currentImage" :stage="stage" />
				<VideoContent
					:retractCountdown="retractCountdown"
					:stage="stage"
					:errorPostureTips="errorPostureTips"
					:isShowErrorPostureTips="isShowErrorPostureTips"
					:totalTimeLeft="totalTimeLeft"
					ref="faceView"
					@faceStatus="handleFaceStatus"
				/>
				<ProcessLine
					:total="detectTotalTime"
					:stage="stage"
					:current="totalTimeLeft"
				/>
				<TipsBox
					:content="errorPostureTips"
					:visible="isShowErrorPostureTips"
				/>
				<div
					:class="
						isShowCountDown ? 'hrvInit-content-tips' : 'opacity-0'
					"
					v-if="stage !== RecordStage.IMAGE_DISPLAY"
				>
					<div class="hrvInit-content-tips-number">
						{{ countNumber }}
					</div>
					<div
						class="hrvInit-content-tips-text"
						:style="{
							fontSize: language.includes('en')
								? '1.7rem'
								: '2.25rem',
						}"
					>
						<p>{{ $t("hrv_test_tip1_key") }}</p>
						<p>{{ $t("hrv_test_tip2_key") }}</p>
						<p>{{ $t("hrv_test_tip3_key") }}</p>
					</div>
				</div>
			</div>
		</template>

		<div class="hrv_audio">
			<audio ref="hrvtestAudioRef" :src="hrvAudioMp3" autoplay></audio>
		</div>

		<div class="hrv_audio">
			<audio
				ref="hrvtestImageTipAudioRef"
				:src="hrvImageTipAudioMp3"
			></audio>
		</div>

		<!-- PPG数据显示区域 -->
		<div class="ppg-data-display" v-if="ppgConnectionStatus !== 'disconnected'">
			<div class="ppg-status">
				<span class="status-label">PPG连接状态:</span>
				<span :class="['status-value', ppgConnectionStatus]">{{ ppgConnectionStatus }}</span>
			</div>

			<div class="ppg-latest-data" v-if="ppgDataBuffer.length > 0">
				<h4>最新PPG数据:</h4>
				<div class="data-item">
					<span>时间戳: {{ ppgDataBuffer[ppgDataBuffer.length - 1]?.timestamp }}</span>
					<span>PPG值: {{ ppgDataBuffer[ppgDataBuffer.length - 1]?.ppgValue }}</span>
				</div>
				<div class="data-count">数据缓冲区大小: {{ ppgDataBuffer.length }}</div>
			</div>

			<div class="rt-param-data" v-if="rtParamBuffer.length > 0">
				<h4>最新实时参数:</h4>
				<div class="param-grid">
					<div class="param-item">SPO2: {{ rtParamBuffer[rtParamBuffer.length - 1]?.data?.spo2 }}</div>
					<div class="param-item">PI: {{ rtParamBuffer[rtParamBuffer.length - 1]?.data?.pi }}</div>
					<div class="param-item">PR: {{ rtParamBuffer[rtParamBuffer.length - 1]?.data?.pr }}</div>
					<div class="param-item">探头脱落: {{ rtParamBuffer[rtParamBuffer.length - 1]?.data?.isProbeOff ? '是' : '否' }}</div>
					<div class="param-item">脉搏搜索: {{ rtParamBuffer[rtParamBuffer.length - 1]?.data?.isPulseSearching ? '是' : '否' }}</div>
				</div>
				<div class="data-count">参数缓冲区大小: {{ rtParamBuffer.length }}</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { useHrvTest } from "./hooks"
import { onMounted, computed, onBeforeMount } from "vue"
import LoadingItems from "./components/LoadingItems.vue"
import VideoImageBox from "./components/VideoImageBox.vue"
import VideoContent from "./components/VideoContent.vue"
import ProcessLine from "./components/ProcessLine.vue"
import { RecordStage } from "../../constants/recordStage.js"
import { getCurrentLanguage } from "@/utils/i18n"
import TipsBox from "./components/TipsBox.vue"
const {
	detectTotalTime,
	stage,
	currentImage,
	totalTimeLeft,
	isShowErrorPostureTips,
	faceView,
	loadingItems,
	isShowCountDown,
	countNumber,
	hrvtestAudioRef,
	hrvAudioMp3,
	hrvtestImageTipAudioRef,
	hrvImageTipAudioMp3,
	errorPostureTips,
	retractCountdown,
  handleLinkEyeTracker,
	handleFaceStatus,
	initHrv,
	// PPG相关
	ppgConnectionStatus,
	ppgDataBuffer,
	rtParamBuffer,
} = useHrvTest()
const language = computed(() => getCurrentLanguage())
onMounted(() => {
  window.onGazeTrackingStarted=(status)=>{
    if(status){
      handleLinkEyeTracker()
      console.log('眼动追踪启动成功')
    }else{
      console.log('眼动追踪启动失败')
    }
  }
	initHrv()
})
</script>
<style lang="scss" scoped>
.hrvInit {
	width: 100vw;
	height: 100vh;
	background-size: 100% 100%;
	background: url("https://img3.airdoc.com/staticResources/data/static/js/hrvinit_back.png");
	font-weight: 500;
	font-size: 1.6rem;
	color: #333333;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	font-family: PingFang SC, PingFang SC;
	overflow: hidden;
	&-generateing {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		img {
			width: 25rem;
			margin-bottom: 8rem;
		}
		span {
			font-weight: 500;
			font-size: 2.25rem;
			color: #666666;
		}
	}
	&-title {
		height: 4.25rem;
		font-weight: 600;
		font-size: 3rem;
		color: #333333;
		line-height: 4.24rem;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 2rem;
	}
	&-content {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		&-tips {
			min-width: 14rem;
			// height: 11.63rem;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			&-number {
				background: url("@/assets/hrv/hrvinit/hrvinit_number_back.png");
				background-size: 100% 100%;
				font-weight: 500;
				font-size: 3.25rem;
				color: #ffffff;
				width: 7.13rem;
				height: 7.13rem;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 1.5rem;
			}
			&-text {
				font-weight: 500;
				font-size: 2.25rem;
				color: #666666;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}
		}
		.opacity-0 {
			opacity: 0;
		}
	}
}

/* PPG数据显示样式 */
.ppg-data-display {
	position: fixed;
	top: 20px;
	right: 20px;
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 15px;
	border-radius: 8px;
	font-size: 12px;
	max-width: 300px;
	z-index: 1000;

	.ppg-status {
		margin-bottom: 10px;

		.status-label {
			font-weight: bold;
		}

		.status-value {
			margin-left: 5px;
			padding: 2px 6px;
			border-radius: 3px;

			&.connected {
				background-color: #4CAF50;
			}

			&.connecting {
				background-color: #FF9800;
			}

			&.error {
				background-color: #F44336;
			}
		}
	}

	h4 {
		margin: 10px 0 5px 0;
		font-size: 14px;
		color: #4CAF50;
	}

	.data-item {
		display: flex;
		flex-direction: column;
		gap: 3px;
		margin-bottom: 5px;

		span {
			font-family: monospace;
		}
	}

	.param-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 5px;
		margin-bottom: 5px;

		.param-item {
			background: rgba(255, 255, 255, 0.1);
			padding: 3px 6px;
			border-radius: 3px;
			font-family: monospace;
		}
	}

	.data-count {
		font-size: 10px;
		color: #ccc;
		margin-top: 5px;
	}
}
</style>
