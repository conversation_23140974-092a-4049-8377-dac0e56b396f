<template>
    <div class="audio-player">
      <div class="controls">
        <el-button type="primary" @click="togglePlay">
          {{ isPlaying ? $t('pause_key') : $t('play_key') }}
        </el-button>
        <!-- <el-slider
          v-model="volume"
          :min="0"
          :max="1"
          :step="0.1"
          @input="handleVolumeChange"
          style="width: 100px; margin: 0 20px;"
        /> -->
      </div>
      <div ref="waveformRef" class="waveform"></div>
      <div class="time-info">
        <span>{{ currentTime }}</span> / <span>{{ duration }}</span>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
  import WaveSurfer from 'wavesurfer'
  
  const props = defineProps({
    audioUrl: {
      type: String,
      required: true
    }
  })
  
  const waveformRef = ref(null)
  const wavesurfer = ref(null)
  const isPlaying = ref(false)
  const volume = ref(1)
  const currentTime = ref('0:00')
  const duration = ref('0:00')
  const audio_url = ref("")
  
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
  
  const handleVolumeChange = (value) => {
    if (wavesurfer.value) {
      wavesurfer.value.setVolume(value)
    }
  }
  
  const togglePlay = () => {
    if (wavesurfer.value) {
      wavesurfer.value.playPause()
    }
  }
  
  onMounted(() => {
    // 初始化 WaveSurfer
    wavesurfer.value = WaveSurfer.create({
      container: waveformRef.value,
      waveColor: '#4a9885',
      progressColor: '#1e88e5',
      cursorColor: '#333',
      barWidth: 2,
      barRadius: 3,
      responsive: true,
      height: 100,
      normalize: true,
      partialRender: true
    })

    setTimeout(() => {
        audio_url.value = props.audioUrl
        console.log(audio_url.value)
    
        // 加载音频
        wavesurfer.value.load(audio_url.value)
    
        // 事件监听
        wavesurfer.value.on('ready', () => {
        duration.value = formatTime(wavesurfer.value.getDuration())
        wavesurfer.value.setVolume(volume.value)
        })
    
        wavesurfer.value.on('audioprocess', () => {
        currentTime.value = formatTime(wavesurfer.value.getCurrentTime())
        })
    
        wavesurfer.value.on('play', () => {
        isPlaying.value = true
        })
    
        wavesurfer.value.on('pause', () => {
        isPlaying.value = false
        })
    }, 1000)

  })
  
  onBeforeUnmount(() => {
    if (wavesurfer.value) {
      wavesurfer.value.destroy()
    }
  })
  watch(audio_url, { deep: true })
  </script>
  
  <style scoped>
  .audio-player {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
  }
  
  .controls {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .waveform {
    margin-bottom: 10px;
  }
  
  .time-info {
    text-align: right;
    color: #666;
    font-size: 14px;
  }
  </style>