// vision-utils.js
import vision from "@mediapipe/tasks-vision";
const { FaceLandmarker, FilesetResolver, DrawingUtils } = vision;

// export async function createFaceLandmarker() {
//   // https://ada-res.airdoc.com/resources/mpd/static/mediapipe/face_landmarker.task  
//   // https://ada-res.airdoc.com/resources/mpd/static/mediapipe/wasm/vision_wasm_internal.js
//   console.log("create FilesetResolver...");
//   const visionResolver = await FilesetResolver.forVisionTasks("https://ada-res.airdoc.com/resources/mpd/static/mediapipe/wasm/vision_wasm_internal.js");
//   console.log("create FilesetResolver successfully.");

//   console.log("create FaceLandmarker ...");
//   const faceLandmarker = await FaceLandmarker.createFromOptions(visionResolver, {
//     baseOptions: {
//       modelAssetPath: "https://ada-res.airdoc.com/resources/mpd/static/mediapipe/face_landmarker.task",
//       delegate: "GPU"
//     },
//     outputFaceBlendshapes: true,
//     runningMode: "VIDEO",
//     numFaces: 1
//   });
//   console.log("create FaceLandmarker successfully.");
//   return faceLandmarker;
// }
export async function createFaceLandmarker() {
  console.log("create FilesetResolver...");
  const visionResolver = await FilesetResolver.forVisionTasks("/mediapipe/wasm");
  console.log("create FilesetResolver successfully.");

  console.log("create FaceLandmarker ...");
  const faceLandmarker = await FaceLandmarker.createFromOptions(visionResolver, {
    baseOptions: {
      modelAssetPath: "/mediapipe/face_landmarker.task",
      delegate: "GPU"
    },
    outputFaceBlendshapes: true,
    runningMode: "VIDEO",
    numFaces: 1
  });
  console.log("create FaceLandmarker successfully.");
  return faceLandmarker;
}
export async function initOpenCV() {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = "https://ada-res.airdoc.com/resources/mpd/static/opencv.js";
    script.type = "text/javascript";
    script.async = true;
    script.onload = () => {
      const checkOpenCV = () => {
        if (window.cv && cv.getBuildInformation) {
          console.log("OpenCV.js is loaded");
          resolve();
        } else {
          console.log("Waiting for OpenCV.js...");
          setTimeout(checkOpenCV, 500);
        }
      };
      checkOpenCV();
    };
    script.onerror = (error) => {
      reject(new Error("Failed to load OpenCV.js" + error));
    };
    document.head.appendChild(script);
  });
}
