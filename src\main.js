import './assets/main.css'

import { createApp } from 'vue'
import router from './router';  // 确保你已经创建了 router 实例
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import App from './App.vue'
import 'element-plus/dist/index.css'; // 默认样式
import { createPinia } from 'pinia'
import i18n from './utils/i18n'
// import * as Sentry from "@sentry/vue";
const app = createApp(App);
// Sentry.init({
//   app,
//   dsn: "https://<EMAIL>/7",
//   integrations: [
//     Sentry.browserTracingIntegration(),
//     Sentry.replayIntegration(),
//   ],
//   // Tracing
//   tracesSampleRate: 1.0, //  Capture 100% of the transactions
//   // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
//   tracePropagationTargets: ["localhost", /^https:\/\/yourserver\.io\/api/],
//   // Session Replay
//   replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
//   replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
// });
app.use(i18n);

const pinia = createPinia()
app.use(pinia)

app.use(ElementPlus);
app.use(router);

app.mount('#app');
