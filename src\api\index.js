import axios from "axios"
import { getCurrentLanguage } from "@/utils/i18n"

// 获取当前语言
// const { t, locale } = useI18n()
export const api = axios.create({
	baseURL: import.meta.env.VITE_API_BASE_URL,
	timeout: 20000,
})

// 拦截器 - 请求发送前
api.interceptors.request.use(
	(config) => {
		// 从 i18n 获取当前语言
		// const lang = locale.value

		let lang = getCurrentLanguage()
		const header_info = {
			"Content-Type": "application/json",
			Authorization: "Bearer " + window.localStorage.getItem("user_token"),
		}
		// 在 headers 中添加语言信息
		config.headers["Accept-Language"] = lang
		config.headers = {
      ...header_info,
			...config.headers,
		}
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

// 拦截器 - 响应接收后
api.interceptors.response.use(
	(response) => {
		// 在这里处理响应数据，例如统一处理错误码
		return response
	},
	(error) => {
		// 处理错误，例如统一错误提示
		return Promise.reject(error)
	}
)
