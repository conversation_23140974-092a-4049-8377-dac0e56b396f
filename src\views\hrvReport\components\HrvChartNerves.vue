<template>
  <table>
    <thead>
    <tr>
      <th colspan="2" class="chart-header">{{ $t('autonomic_nervous_analysis_key')}}</th>
    </tr>
    </thead>
    <tbody>
    <tr>
      <!-- Iterate over the NerveInfo dictionary -->
      <td
          v-for="(info, key) in NerveInfo"
          :key="key"
          class="chart-cell"
      >
        <PressureChart
            :index="key"
            :title="info.title"
            :compareDescription="info.rank"
            :value="info.value"
            :barMax="info.max"
            :barMin="info.min"
            :barValue="info.value"
            :description="info.description"
        />
      </td>
    </tr>
    </tbody>
  </table>
</template>

<script setup>
// Import the PressureChart component
import PressureChart from './PressureChart.vue';

// Define the prop NerveInfo, which is a dictionary
const props = defineProps({
  NerveInfo: {
    type: Object,
    required: true,
  },
});
</script>

<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}

th, td {
  padding: 10px;
  border: 1px solid #ddd;
}

.chart-cell {
  width: 50%; /* Each chart takes half of the row */
  vertical-align: top; /* Align content to the top */
}

.chart-header {
  text-align: left !important; /* 强制左对齐 */
  background-color: #17d7e2; /* 深绿色背景 */
  color: white;
  font-weight: bold;
}
</style>
