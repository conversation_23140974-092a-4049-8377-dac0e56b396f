pipeline {
    agent {
        node {
            label "docker-ubuntu2204-01"
        }
    }
    tools {
      nodejs "node@18.20.7"
    }
    environment {
        GIT_REPOSITORY = 'https://gitlab.airdoc.com/tsemo'
        // 设置npm缓存目录为工作空间内的目录
        NPM_CONFIG_CACHE = "${WORKSPACE}/.npm-cache"
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
    }
    parameters {
        gitParameter branch: '', branchFilter: 'origin/(.*)', defaultValue: 'develop', description: 'mpd_app_h5Branch', name: 'codeBranch', quickFilterEnabled: true, selectedValue: 'NONE', sortMode: 'NONE', tagFilter: '', type: 'PT_BRANCH', useRepository: 'https://gitlab.airdoc.com/tsemo/mpd_app_h5.git'
        string(name: 'serverPath', defaultValue: '/home/<USER>/shiqi/fe-mpd-app-h5', description: '远程部署路径')
    }

    stages {
        stage('Initialize'){
            steps{
                echo "准备构建，Node 版本如下："
                sh 'node -v'
                sh 'mkdir -p ${NPM_CONFIG_CACHE}'
            }
        }
        stage("Pull Code from Gitlab") {
            steps {
                dir("mpd_app_h5") {
                    git branch: "${params.codeBranch}", credentialsId: '550e15c8-32e5-47fb-9ddf-e0e6eee8c2ac', url: "${GIT_REPOSITORY}/mpd_app_h5.git"
                }
            }
        }
        stage("Build Npm") {
            steps {
                dir("mpd_app_h5") {
                    script {
                        sh '''
                            # 清理可能存在的旧依赖和锁文件
                            rm -rf node_modules
                            rm -f package-lock.json

                            # 设置npm缓存和注册表
                            npm config set cache "${NPM_CONFIG_CACHE}"
                            npm config set registry https://registry.npmmirror.com/

                            # 强制重新安装所有依赖
                            npm install --force

                            # 执行构建
                            npm run build:${buildEnv}
                        '''
                    }
                }
            }
        }
        stage('Deploy') {
            steps {
                sshagent(['20242b58-bf74-4422-8eb5-380dd668b19f']) {
                    sh '''
                    cd mpd_app_h5
                    ansible-playbook -i deploy/hosts deploy/playbook/upload_front.yaml \
                      -e 'ansible_ssh_common_args="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"' \
                      -e "target_env=test remote_path=${serverPath}"
                    '''
                }
            }
        }
        stage("Test") {
            steps {
                sh '''
                echo 'Test'
                '''
            }
        }
    }
    post {
        success {
            sh '''
            echo success
            '''
        }
        failure {
            sh '''
            echo failure
            '''
        }
    }
}
