<template>
    <div class="speech-test">
        <h1 class="fixed-header">{{$t('speech_sys_title_key')}}</h1>

        <div class="content">
        <div v-if="!isGeneratingReport && !isHiddenReportFlag" class="introduction">
            <!-- <h3>{{$t('speech_sys_desc_key')}}</h3> -->
            <!-- <p>{{$t('speech_sys_desc_welcome_key')}}</p>
            <ul>
            <li>{{$t('speech_sys_desc_li1_key')}}</li>
            </ul>
            <p>{{$t('speech_sys_desc_pre_start_key')}}</p> -->
            <p>{{$t('speech_sys_desc_welcome_key1')}}</p>
            <p>{{$t('speech_sys_desc_welcome_key2')}}</p>
        </div>
        </div>

        <div v-if="!isGeneratingReport && !isHiddenReportFlag" class="audio-recorder content">
            <div class="recorder-card">
            <div class="recorder-header">
                <div class="header-left">
                <h3>{{ isRecording ? t('recording_key') : t('record_action_key') }}</h3>
                <span class="time">{{ formattedTime }}</span>
                </div>
                <div class="select-div">
                  <!-- 语言选择 -->
                  <div class="language-select">
                  <el-select v-model="language" size="small" class="lang-select">
                      <el-option label="中文" value="中文" />
                      <el-option label="English" value="en" />
                  </el-select>
                  </div>
                   <!-- 降噪选择 -->
                  <div class="noise-checkbox">
                  <el-checkbox v-model="noise" size="small" class="noise-select-checkbox">
                    {{$t('speech_sys_denoise_key')}}
                  </el-checkbox>
                  </div>
                </div>
            </div>

            <!-- 波形显示区域 -->
            <div class="wave-container">
                <div ref="waveformRef" class="waveform" v-show="!isRecording"></div>
                <canvas ref="canvas" class="wave-canvas" v-show="isRecording"></canvas>
            </div>

            <div class="controls">
                <!-- 录音按钮 -->
                <button
                :type="isRecording ? 'danger' : 'primary'"
                @click="toggleRecording"
                :icon="isRecording ? 'el-icon-video-pause' : 'el-icon-video-play'"
                :disabled="isPlaying || isAnalyzing"
                >
                {{ isRecording ? t('stop_recording_key') : t('start_recording_key') }}
                </button>

                <!-- 播放按钮 -->
                <button
                type="success"
                @click="togglePlay"
                :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
                :disabled="!audioBlob || isRecording"
                >
                {{ isPlaying ? t('pause_key') : t('play_key') }}
                </button>

                <!-- 保存按钮 -->
                <button
                type="primary"
                @click="saveRecording"
                :disabled="!audioBlob || isRecording || isAnalyzing"
                icon="el-icon-upload"
                >
                {{$t('speech_sys_vioce_analyze_key')}}
                </button>
            </div>
            </div>
        </div>

        <div v-if="isGeneratingReport" class="report-generation">
            <p>{{$t('report_generating_key')}}</p>
            <div class="progress-bar-container">
                <div class="progress-bar">
                <div class="progress" :style="{ width: `${progressPercentage}%` }"></div>
                </div>
            </div>
        </div>

        <div v-if="isHiddenReportFlag" class="generate-report-complete-later">
          <p>{{ $t('report_generate_complete_report_later_key')}}</p>
          <button @click="handleExit" class="exit-button">{{ $t('return_key')}}</button>
        </div>
        
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import WaveSurfer from 'wavesurfer'
import { api } from '@/api';

import { useRouter } from 'vue-router'
import { useSpeechStore } from '@/stores/speechStore'
import { getCurrentLanguage } from '@/utils/i18n'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const router = useRouter();
const speechStore = useSpeechStore();
const analysisResults = ref({})

const CANVAS_HEIGHT = 150
const BAR_WIDTH = 2
const BAR_GAP = 1
const BAR_COLOR = '#1976D2'
const WAVE_COLOR = '#4CAF50'
const PROGRESS_COLOR = '#1976D2'

// 状态变量
const isRecording = ref(false)
const isPlaying = ref(false)
const audioBlob = ref(null)
const recordingTime = ref(0)
const waveformRef = ref(null)
const canvas = ref(null)
const wavesurfer = ref(null)
const recordingInterval = ref(null)
const language = ref("中文")
const noise = ref(false)
const isAnalyzing = ref(false)

const deviceId = ref("")
const isHiddenReportFlag = ref(0)


// 音频相关变量
let audioContext = null
let analyser = null
let dataArray = null
let mediaRecorder = null
let audioStream = null
let animationFrame = null
let audioChunks = []

const reportStartTime = ref(0);
const reportDuration = 5000; // 5 seconds in milliseconds
const reportTimeoutId = ref(null);
const isGeneratingReport = ref(false)
const reportProgress = ref(0)
const progressPercentage = computed(() => reportProgress.value * 100);

const animateProgress = (timestamp) => {
  if (!isGeneratingReport.value) return;

  const elapsed = timestamp - reportStartTime.value;
  reportProgress.value = Math.min(elapsed / reportDuration, 1);

  if (reportProgress.value < 1) {
    requestAnimationFrame(animateProgress);
  }
}

// 格式化时间显示
const formattedTime = computed(() => {
  const minutes = Math.floor(recordingTime.value / 60)
  const seconds = recordingTime.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

const handleExit = () => {
  if (typeof android !== 'undefined' && android && typeof android.onFinish === 'function') {
    android.onFinish();
  } else {
    const params = new URLSearchParams(window.location.search)
    const urlParam = params.toString()
    router.replace('/speech?' + urlParam).then(() => {
      window.location.reload();  // 强制页面刷新，重置所有组件
    });
  }
};

// 初始化波形显示器
onMounted(() => {

  const urlParams = new URLSearchParams(window.location.search);
  const device_id = urlParams.get('device_id');
  if (device_id) {
    deviceId.value = device_id
  }
  const lang = getCurrentLanguage()
  if (lang != 'zh') {
    language.value = "en"
  }

  const user_token = urlParams.get('token');
  if (user_token) {
    window.localStorage.setItem("user_token", user_token)
  }

  initWaveSurfer()
  initCanvas()
//   const result = checkSupportedFormats()
//   console.log(result)
//   isGeneratingReport.value = false;
//   reportProgress.value = 0;
//   reportStartTime.value = 0;
//   speechStore.resetState();

})

// 检查浏览器支持的格式
const checkSupportedFormats = () => {
  const types = [
    'audio/webm',
    'audio/webm;codecs=opus',
    'audio/wav',
    'audio/mp3'
  ];
  
  return types.filter(type => MediaRecorder.isTypeSupported(type));
}

// 初始化 WaveSurfer
const initWaveSurfer = () => {
  wavesurfer.value = WaveSurfer.create({
    container: waveformRef.value,
    waveColor: WAVE_COLOR,
    progressColor: PROGRESS_COLOR,
    height: CANVAS_HEIGHT,
    barWidth: BAR_WIDTH,
    barGap: BAR_GAP,
    cursorWidth: 0,
    normalize: true,
    responsive: true
  })

  wavesurfer.value.on('finish', () => {
    isPlaying.value = false
  })
}

// 初始化 Canvas
const initCanvas = () => {
  const ctx = canvas.value.getContext('2d')
  canvas.value.height = CANVAS_HEIGHT
  canvas.value.width = canvas.value.parentElement.offsetWidth

  // 设置初始状态
  ctx.fillStyle = BAR_COLOR
  ctx.strokeStyle = BAR_COLOR
}

// 开始录音
const startRecording = async () => {
  try {
    audioStream = await navigator.mediaDevices.getUserMedia({ audio: true })
    audioChunks = []
    
    // 设置音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)()
    analyser = audioContext.createAnalyser()
    const source = audioContext.createMediaStreamSource(audioStream)
    source.connect(analyser)
    
    // 配置分析器
    analyser.fftSize = 2048
    const bufferLength = analyser.frequencyBinCount
    dataArray = new Float32Array(bufferLength)
    
    // 开始录音
    mediaRecorder = new MediaRecorder(audioStream, {
        mimeType: 'audio/webm;codecs=opus',
        bitsPerSecond: 128000
    })
    mediaRecorder.ondataavailable = (event) => {
      audioChunks.push(event.data)
    }
    
    mediaRecorder.onstop = () => {
        const blob = new Blob(audioChunks, { type: 'audio/webm' })
        audioBlob.value = blob
        wavesurfer.value.loadBlob(blob)
    }

    mediaRecorder.start()
    isRecording.value = true
    startTimer()
    drawWave()

  } catch (error) {
    ElMessage.error(t('speech_sys_noaccess_mic_key'))
    console.error('录音失败', error)
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder && isRecording.value) {
    mediaRecorder.stop()
    audioStream.getTracks().forEach(track => track.stop())
    
    stopTimer()
    isRecording.value = false
    
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
    }
    
    if (audioContext) {
      audioContext.close()
    }
  }
}

// 绘制波形
const drawWave = () => {
  if (!isRecording.value) return

  animationFrame = requestAnimationFrame(drawWave)
  const ctx = canvas.value.getContext('2d')
  const width = canvas.value.width
  const height = canvas.value.height
  
  analyser.getFloatTimeDomainData(dataArray)
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 计算条形图参数
  const totalBars = Math.floor(width / (BAR_WIDTH + BAR_GAP))
  const samplesPerBar = Math.floor(dataArray.length / totalBars)
  
  // 绘制条形图
  for (let i = 0; i < totalBars; i++) {
    let sum = 0
    for (let j = 0; j < samplesPerBar; j++) {
      const index = i * samplesPerBar + j
      if (index < dataArray.length) {
        sum += Math.abs(dataArray[index])
      }
    }
    
    // 计算平均振幅
    const amplitude = (sum / samplesPerBar) * height
    const barHeight = Math.max(1, Math.min(amplitude * 2, height))
    
    // 绘制对称的条形
    const x = i * (BAR_WIDTH + BAR_GAP)
    const y = (height - barHeight) / 2
    
    ctx.fillRect(x, y, BAR_WIDTH, barHeight)
  }
}

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

// 切换播放状态
const togglePlay = () => {
  if (wavesurfer.value) {
    wavesurfer.value.playPause()
    isPlaying.value = !isPlaying.value
  }
}

// 计时器相关
const startTimer = () => {
  recordingTime.value = 0
  recordingInterval.value = setInterval(() => {
    recordingTime.value++
  }, 1000)
}

const stopTimer = () => {
  if (recordingInterval.value) {
    clearInterval(recordingInterval.value)
    recordingInterval.value = null
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopRecording()
  if (wavesurfer.value) {
    wavesurfer.value.destroy()
  }
})

// 上传并分析
const saveRecording = async () => {
    if (!audioBlob.value) {
        ElMessage.warning(t('speech_sys_warning_recording_key'))
        return
    }
    isAnalyzing.value = true
    try {
        // 上传音频文件
        const formData = new FormData()
        formData.append('audio', audioBlob.value, 'recording.webm')
        
        const uploadResponse = await api.post('/api/v1/screening/speech_upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            // onUploadProgress: (progressEvent) => {
            //     uploadProgress.value = Math.round(
            //         (progressEvent.loaded * 100) / progressEvent.total
            //     )
            // }
        })
        if (uploadResponse.data.code !== 0) {
            throw new Error(uploadResponse.data.message)
        }

        const header_info = {
          headers: {
            'DEVICE-ID': deviceId.value,
            'TS-Auth-Token': window.localStorage.getItem('user_token')
          }
        }
        
        // 发送分析请求
        const analysisResponse = await api.post('/api/v1/screening/speech_analyze', {
            audio_url: uploadResponse.data.data.url,
            language: language.value,
            blob_name: uploadResponse.data.data.blob_name,
            noise: noise.value == false ? 0 : 1
        }, header_info)
        
        if (analysisResponse.data.code !== 0) {
            throw new Error(analysisResponse.data.message)
        }
        
        analysisResults.value = analysisResponse.data.data

        handle_success()

    } catch (err) {
        console.log(`分析语音失败: ${err.message}`)
        ElMessage.error(t('speech_sys_analyze_failed_key') + `: ${err.message}`)
    } finally {
        isAnalyzing.value = false
    }
}

const handle_success = () => {
    console.log("analysisResults.value: " + analysisResults.value)
    if (analysisResults.value) {
        isGeneratingReport.value = true;
        reportStartTime.value = performance.now();
        reportProgress.value = 0;

        reportTimeoutId.value = setTimeout(() => {
            isGeneratingReport.value = false;
            console.log(analysisResults.value)
            speechStore.setAnalysisResults(analysisResults.value);
            if (analysisResults.value.is_hidden_report) {
              isHiddenReportFlag.value = analysisResults.value.is_hidden_report
              return;
            }

            router.push({name: 'SpeechReport', query: {"en_screening_id": analysisResults.value.en_screening_id}});
        }, reportDuration);

        requestAnimationFrame(animateProgress);
    }
}

</script>

<style scoped>

.speech-test {
  width: 800px;
  height: 100vh;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: left;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px 0;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  margin-top: 60px; /* Adjust this value based on the height of your header */
  padding: 20px;
}

.introduction {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.introduction h3 {
  color: #333;
  margin-bottom: 15px;
}

.introduction ul {
  padding-left: 20px;
}

.introduction ul ul {
  margin-top: 10px;
}

.introduction p:last-of-type {
  margin-top: 10px; /* Increase this value for more spacing */
}


.audio-recorder {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.recorder-card {
  padding: 20px;
}

.recorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h3 {
  margin: 0;
  font-size: 1.2em;
  color: #333;
}

.select-div {
  display: flex;
}

.language-select {
  min-width: 120px;
}

.lang-select {
  width: 100%;
}

.noise-checkbox {
  margin-left: 1rem;
}

.time {
  font-size: 1.1em;
  font-family: monospace;
  color: #666;
}

.wave-container {
  width: 100%;
  height: 150px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
  overflow: hidden;
  position: relative;
}

.waveform {
  width: 100%;
  height: 100%;
}

.wave-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 600px) {
  .controls {
    flex-direction: column;
  }
  
  .controls button {
    width: 100%;
  }
}

button {
  min-width: 120px;
  padding: 12px 24px;
  font-size: 16px;
  background-color: #17d7ea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button:hover {
  background-color: #14bccf;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

button.btn-stop {
  background-color: #f44336;
}

button.btn-stop:hover {
  background-color: #d32f2f;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  box-shadow: none;
}

.report-generation {
  width: 100%;
  margin-top: 20px;
  text-align: center;
}

.report-generation p {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
}

.progress-bar-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.progress-bar {
  width: 60%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.5s linear;
}

.generate-report-complete-later {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content:  flex-start;
  margin-top: 20px;
  gap: 20px;
  height: 100%; /* Fill the main container */
  text-align: center;
  font-size: 1.5rem;
  color: #19191a;
}
</style>