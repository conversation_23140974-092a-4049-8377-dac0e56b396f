// // vision-utils.js
import vision from "@mediapipe/tasks-vision";
const { FaceLandmarker, FilesetResolver, DrawingUtils } = vision

export async function createFaceLandmarker() {
	console.log("create FilesetResolver...")

	if (window.AndroidDebug) {
		window.AndroidDebug.logFromJS("Starting FilesetResolver creation")
	}

	const visionResolver = await FilesetResolver.forVisionTasks(
		"https://ada-res.airdoc.com/resources/mpd/static/mediapipe/wasm"
	)
	console.log("create FilesetResolver successfully.")

	console.log("create FaceLandmarker ...")
	const modelPath = "https://ada-res.airdoc.com/resources/mpd/static/mediapipe/face_landmarker.task"
	console.log("Loading face_landmarker.task from:", modelPath)

	if (window.AndroidDebug) {
		window.AndroidDebug.logFromJS("Loading face_landmarker.task from: " + modelPath)
	}

	const startTime = performance.now()

	const faceLandmarker = await FaceLandmarker.createFromOptions(
		visionResolver,
		{
			baseOptions: {
				modelAssetPath: modelPath,
				delegate: "GPU",
			},
			outputFaceBlendshapes: true,
			runningMode: "VIDEO",
			numFaces: 1,
		}
	)

	const loadTime = performance.now() - startTime
	console.log(`create FaceLandmarker successfully in ${loadTime.toFixed(2)}ms`)

	if (window.AndroidDebug) {
		window.AndroidDebug.logFromJS(`FaceLandmarker created successfully in ${loadTime.toFixed(2)}ms`)
	}

	return faceLandmarker
}
export async function initOpenCV() {
	return new Promise((resolve, reject) => {
		const script = document.createElement("script")
		const opencvUrl = "https://ada-res.airdoc.com/resources/mpd/static/opencv.js"
		script.src = opencvUrl
		script.type = "text/javascript"
		script.async = true

		console.log("Loading OpenCV.js from:", opencvUrl)
		console.log("Script element created, attempting to load...")

		// 添加Android调试日志
		if (window.AndroidDebug) {
			window.AndroidDebug.logFromJS("Starting to load OpenCV.js from: " + opencvUrl)
		}

		const startTime = performance.now()

		script.onload = () => {
			const loadTime = performance.now() - startTime
			console.log(`OpenCV.js script loaded successfully in ${loadTime.toFixed(2)}ms`)

			if (window.AndroidDebug) {
				window.AndroidDebug.logFromJS(`OpenCV.js loaded in ${loadTime.toFixed(2)}ms`)
			}

			const checkOpenCV = () => {
				if (window.cv && cv.getBuildInformation) {
					console.log("OpenCV.js is loaded and ready")
					if (window.AndroidDebug) {
						window.AndroidDebug.logFromJS("OpenCV.js initialization completed successfully")
					}
					resolve()
				} else {
					console.log("Waiting for OpenCV.js to initialize...")
					setTimeout(checkOpenCV, 500)
				}
			}
			checkOpenCV()
		}
		script.onerror = (error) => {
			console.error("Failed to load OpenCV.js script:", error)
			if (window.AndroidDebug) {
				window.AndroidDebug.logFromJS("Failed to load OpenCV.js: " + error.toString())
			}
			reject(new Error("Failed to load OpenCV.js: " + error))
		}
		document.head.appendChild(script)
		console.log("OpenCV.js script added to document head")
	})
}
