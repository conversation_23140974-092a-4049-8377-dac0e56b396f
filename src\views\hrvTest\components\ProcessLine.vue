<template>
	<div class="process-line" v-if="stage === RecordStage.IMAGE_DISPLAY">
		<div
			class="process-line-inner"
			:style="{ width: ((total - current) / total) * 100 + '%' }"
		></div>
	</div>
</template>

<script setup>
import { RecordStage } from "@/constants/recordStage"
import { watch } from "vue"

const props = defineProps({
	total: {
		type: Number,
		default: 90,
		required: true,
	},
	current: {
		type: Number,
		default: 0,
		required: true,
	},
	stage: {
		type: Number,
		default: 0,
		required: true,
	},
})
</script>

<style lang="scss" scoped>
.process-line {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 0.63rem;
	background: rgba(235, 78, 137, 0.4);
	&-inner {
		background: #eb4e89;
		border-radius: 2rem;
		position: absolute;
		bottom: 0;
		left: 0;
		height: 0.63rem;
	}
}
</style>
