<template>
	<div id="app">
		<router-view :key="$route.fullPath" />
	</div>
</template>

<script setup>
import { onBeforeMount, onMounted, ref, watch } from "vue"
import { changeLanguage } from "@/utils/i18n.js"
import { useRoute } from "vue-router"
import initRem from "./utils/rem"
import { getAndroidInitParams } from "./utils/android"
initRem()
const route = useRoute()
const bodyBackgroundColor = ref("#f3f4f5")

onBeforeMount(() => {
	initLanguage()
})
const initLanguage = () => {
	const urlParams = new URLSearchParams(window.location.search)
	const urlLanguage = urlParams.get("language")
  if (urlLanguage) {
		return changeLanguage(urlLanguage.includes("zh") ? "zh-CN" : "en-US")
	}
	const initData = getAndroidInitParams()
	const initLanguage = initData?.language || navigator.language
	let lang
	if (initLanguage == "en-US") {
		lang = "en-US"
	} else {
		lang = "zh-CN"
	}
	changeLanguage(lang)
}
watch(
	() => route.path,
	(newName) => {
		if (newName.includes("hrvinit")) {
			bodyBackgroundColor.value = "transparent"
		} else {
			bodyBackgroundColor.value = "#f3f4f5"
		}
	},
	{ immediate: true }
)

watch(
	bodyBackgroundColor,
	(newColor) => {
		document.documentElement.style.setProperty(
			"--body-background-color",
			newColor
		)
	},
	{ immediate: true }
)
</script>

<style>
#app {
	display: flex;
	flex-direction: row;
	min-height: 100%;
	min-width: 100%;
	border-radius: 3.13rem;
}

body {
	background-color: var(--body-background-color, transparent);
	color: black;
}

#app > .router-view {
	flex: 1;
}

@media print {
	* {
		-webkit-print-color-adjust: exact; /* 针对 WebKit 浏览器 */
		print-color-adjust: exact; /* 标准属性 */
	}
	#app {
		padding: 0;
		background-color: white;
	}
	body {
		background-color: white;
	}
}
</style>
