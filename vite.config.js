// import { sentryVitePlugin } from "@sentry/vite-plugin";
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  // , sentryVitePlugin({
  //   org: "sentry",
  //   project: "hrv-fe",
  //   url: "https://sentry.airdoc.com/"
  // })
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },

  server: {
    proxy: {
      '/speedtest': {
        target: 'https://ada-res.airdoc.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/speedtest/, '')
      }
    }
  },

  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // 例如：vue、element-plus、echarts等会被单独分包
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        }
      }
    }
  }
})